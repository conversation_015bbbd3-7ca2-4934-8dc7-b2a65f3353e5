# UDP Listener for Multi-Sensor Tool Testing
# PowerShell script to listen for UDP data from radar/lidar sensors

param(
    [int]$Port = 9001,
    [string]$LogFile = "",
    [switch]$ShowTimestamp = $true,
    [switch]$ShowRaw = $false,
    [switch]$Help
)

if ($Help) {
    Write-Host "UDP Listener for Multi-Sensor Tool Testing"
    Write-Host ""
    Write-Host "Usage: .\udp_listener.ps1 [-Port <port>] [-LogFile <file>] [-ShowTimestamp] [-ShowRaw] [-Help]"
    Write-Host ""
    Write-Host "Parameters:"
    Write-Host "  -Port <port>      UDP port to listen on (default: 9001)"
    Write-Host "  -LogFile <file>   Optional log file to save received data"
    Write-Host "  -ShowTimestamp    Show timestamp for each message (default: true)"
    Write-Host "  -ShowRaw          Show raw bytes in addition to text"
    Write-Host "  -Help             Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\udp_listener.ps1                    # Listen on port 9001"
    Write-Host "  .\udp_listener.ps1 -Port 9002         # Listen on port 9002"
    Write-Host "  .\udp_listener.ps1 -LogFile log.txt   # Save to log file"
    Write-Host ""
    Write-Host "To test with multi-sensor tool:"
    Write-Host "  1. Start this listener: .\udp_listener.ps1 -Port 9001"
    Write-Host "  2. In another terminal: .\udp_listener.ps1 -Port 9002"
    Write-Host "  3. Run the tool: multi_sensor_tool.exe -f test_config.txt -g json-only-test -d"
    exit 0
}

Write-Host "===== UDP Listener for Multi-Sensor Tool ====="
Write-Host "Listening on UDP port $Port"
Write-Host "Press Ctrl+C to stop"
Write-Host ""

# 创建UDP客户端
try {
    $udpClient = New-Object System.Net.Sockets.UdpClient($Port)
    $endpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Any, 0)
    
    Write-Host "UDP listener started successfully on port $Port"
    if ($LogFile) {
        Write-Host "Logging to file: $LogFile"
    }
    Write-Host "Waiting for data..."
    Write-Host ""
    
    $messageCount = 0
    
    while ($true) {
        try {
            # 接收数据
            $receivedBytes = $udpClient.Receive([ref]$endpoint)
            $receivedData = [System.Text.Encoding]::UTF8.GetString($receivedBytes)
            $messageCount++
            
            # 构建输出消息
            $output = ""
            
            if ($ShowTimestamp) {
                $timestamp = Get-Date -Format "HH:mm:ss.fff"
                $output += "[$timestamp] "
            }
            
            $output += "Message #$messageCount from $($endpoint.Address):$($endpoint.Port)"
            
            # 显示消息
            Write-Host $output -ForegroundColor Green
            
            # 显示原始字节（如果启用）
            if ($ShowRaw) {
                $hexBytes = ($receivedBytes | ForEach-Object { $_.ToString("X2") }) -join " "
                Write-Host "  Raw bytes: $hexBytes" -ForegroundColor Gray
                Write-Host "  Length: $($receivedBytes.Length) bytes" -ForegroundColor Gray
            }
            
            # 显示JSON数据（格式化）
            try {
                $jsonObject = $receivedData | ConvertFrom-Json
                Write-Host "  JSON Data:" -ForegroundColor Yellow
                Write-Host "    Timestamp: $($jsonObject.timestamp)" -ForegroundColor Cyan
                Write-Host "    Sensor Type: $($jsonObject.sensor_type)" -ForegroundColor Cyan
                
                if ($jsonObject.data) {
                    Write-Host "    Data:" -ForegroundColor Cyan
                    $jsonObject.data | ConvertTo-Json -Depth 10 | ForEach-Object {
                        Write-Host "      $_" -ForegroundColor White
                    }
                }
            }
            catch {
                # 如果不是有效的JSON，直接显示原始数据
                Write-Host "  Raw Data: $receivedData" -ForegroundColor White
            }
            
            Write-Host ""
            
            # 写入日志文件（如果指定）
            if ($LogFile) {
                $logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss.fff') - Message #$messageCount from $($endpoint.Address):$($endpoint.Port)"
                $logEntry += "`n$receivedData`n"
                Add-Content -Path $LogFile -Value $logEntry
            }
        }
        catch [System.Net.Sockets.SocketException] {
            Write-Host "Socket error: $($_.Exception.Message)" -ForegroundColor Red
            break
        }
        catch {
            Write-Host "Error receiving data: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}
catch [System.Net.Sockets.SocketException] {
    Write-Host "Failed to bind to port $Port. Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Possible causes:" -ForegroundColor Yellow
    Write-Host "  - Port is already in use" -ForegroundColor Yellow
    Write-Host "  - Insufficient permissions" -ForegroundColor Yellow
    Write-Host "  - Firewall blocking the port" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Try:" -ForegroundColor Green
    Write-Host "  - Use a different port: .\udp_listener.ps1 -Port 9003" -ForegroundColor Green
    Write-Host "  - Run as Administrator" -ForegroundColor Green
    Write-Host "  - Check firewall settings" -ForegroundColor Green
    exit 1
}
catch {
    Write-Host "Unexpected error: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
finally {
    if ($udpClient) {
        $udpClient.Close()
        Write-Host "UDP listener stopped" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Total messages received: $messageCount"
Write-Host "UDP listener terminated"
