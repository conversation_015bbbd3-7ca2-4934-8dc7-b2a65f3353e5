#ifndef MULTI_SENSOR_SERVER_H
#define MULTI_SENSOR_SERVER_H

#include <chrono>
#include <iostream>
#include <thread>
#include <vector>
#include <fstream>
#include <string>
#include <cstdint>
#include <stdexcept>
#include <sstream>
#include <algorithm>
#include <memory>
#include <functional>
#include <unordered_map>

// 网络相关头文件
#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
#endif

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/time.h>
}

// 复用原有的StreamContext结构
struct StreamContext {
    AVFormatContext *input_ctx = nullptr;
    AVFormatContext *output_ctx = nullptr;
    AVPacket pkt;
    int64_t last_dts[AV_NUM_DATA_POINTERS] = {0};
    int retry_count = 0;
    std::string input_file_ts;
    std::string input_file_txt;
    std::vector<int64_t> time_base;
    int64_t frame_seq = 0;
    int64_t correction_frame_seq = 0;
    std::string output_url;
    int64_t average_interval = 3600;
    int64_t average_interval_ms = 40;
    int64_t time_count_ms = 0;
    bool is_playing = false;

    StreamContext(const std::string &input_ts, const std::string &input_txt, const std::string &output)
        : input_file_ts(input_ts), input_file_txt(input_txt), output_url(output) {}
};

// 简化错误处理
class SimpleErrorHandler {
public:
    enum ErrorLevel { INFO, WARNING, ERROR, FATAL };
    
    static void logError(ErrorLevel level, const std::string& message) {
        std::string prefix = getErrorPrefix(level);
        std::cerr << "[" << getCurrentTime() << "] " << prefix << message << std::endl;
        
        if (level == FATAL) {
            exit(1);
        }
    }
    
    static bool retryOperation(std::function<bool()> operation, int max_retries = 3) {
        for (int i = 0; i < max_retries; ++i) {
            if (operation()) {
                return true;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        return false;
    }
    
private:
    static std::string getErrorPrefix(ErrorLevel level) {
        switch (level) {
            case INFO: return "[INFO] ";
            case WARNING: return "[WARN] ";
            case ERROR: return "[ERROR] ";
            case FATAL: return "[FATAL] ";
            default: return "[UNKNOWN] ";
        }
    }
    
    static std::string getCurrentTime() {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()) % 1000;
        
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%H:%M:%S");
        ss << "." << std::setfill('0') << std::setw(3) << ms.count();
        return ss.str();
    }
};

// 传感器抽象接口（最小化）
class SensorBase {
public:
    enum SensorType { VIDEO, FISHEYE_VIDEO, RADAR, LIDAR };
    
    virtual ~SensorBase() = default;
    
    // 核心接口
    virtual bool initialize() = 0;
    virtual bool processFrame(int64_t current_time) = 0;
    virtual int64_t getCurrentTimestamp() = 0;
    virtual bool hasMoreData() = 0;
    virtual SensorType getType() = 0;
    
    // 状态查询
    virtual bool isInitialized() const = 0;
    virtual std::string getDataFile() const = 0;
    virtual std::string getOutputAddress() const = 0;
    
    // 循环播放支持
    virtual void reset() = 0;
};

// 简单UDP发送器
class SimpleUDPSender {
private:
    int socket_fd_ = -1;
    struct sockaddr_in server_addr_;
    std::string target_address_;
    int target_port_;
    bool initialized_ = false;
    
public:
    SimpleUDPSender(const std::string& address, int port);
    ~SimpleUDPSender();
    
    bool initialize();
    bool sendData(const std::string& data);
    void cleanup();
    
private:
    bool createSocket();
    bool setupAddress();
};

// JSON传感器
class JsonSensor : public SensorBase {
public:
    struct JsonFrame {
        int64_t timestamp;
        std::string data;
        
        JsonFrame(int64_t ts, const std::string& d) : timestamp(ts), data(d) {}
    };
    
private:
    std::vector<JsonFrame> frames_;
    size_t current_index_ = 0;
    SensorType sensor_type_;
    std::string json_file_;
    std::string udp_address_;
    int udp_port_;
    std::unique_ptr<SimpleUDPSender> udp_sender_;
    bool initialized_ = false;
    
public:
    JsonSensor(const std::string& json_file, const std::string& udp_address, 
               int udp_port, SensorType type);
    
    bool initialize() override;
    bool processFrame(int64_t current_time) override;
    int64_t getCurrentTimestamp() override;
    bool hasMoreData() override;
    SensorType getType() override { return sensor_type_; }
    bool isInitialized() const override { return initialized_; }
    std::string getDataFile() const override { return json_file_; }
    std::string getOutputAddress() const override { return udp_address_ + ":" + std::to_string(udp_port_); }
    void reset() override;
    
private:
    bool loadJsonData();
    void parseJsonFile(const std::string& file_path);
    std::string createOutputMessage(const JsonFrame& frame);
};

// 视频传感器（包装原有逻辑）
class VideoSensor : public SensorBase {
private:
    StreamContext stream_context_;
    SensorType sensor_type_;
    bool initialized_ = false;
    bool is_loop_;
    bool is_debug_;
    uint16_t camera_type_;
    int64_t earliest_timestamp_;
    
public:
    VideoSensor(const std::string& ts_file, const std::string& txt_file, 
                const std::string& rtsp_url, SensorType type = VIDEO,
                bool is_loop = false, bool is_debug = false, uint16_t camera_type = 0,
                int64_t earliest_timestamp = 0);
    
    bool initialize() override;
    bool processFrame(int64_t current_time) override;
    int64_t getCurrentTimestamp() override;
    bool hasMoreData() override;
    SensorType getType() override { return sensor_type_; }
    bool isInitialized() const override { return initialized_; }
    std::string getDataFile() const override { return stream_context_.input_file_ts; }
    std::string getOutputAddress() const override { return stream_context_.output_url; }
    void reset() override;
    
private:
    bool shouldProcessFrame(int64_t current_time, int64_t time_interval);
    void handleVideoFrame();
    
    // 复用原有的辅助函数
    int init_stream();
    std::vector<int64_t> readFileToInt64(const std::string& filePath);
    void handle_timestamps();
    void process_packet(AVPacket *pkt);
    uint8_t* create_sei_frame(size_t *out_size);
    int64_t get_timestamp_ms();
};

// 配置解析器
class SimpleConfigParser {
public:
    struct SensorConfig {
        std::string type;           // "video", "fisheye", "radar", "lidar"
        std::string data_file;      // 数据文件路径
        std::string timestamp_file; // 时间戳文件路径（JSON类型为空）
        std::string output_address; // 输出地址
        int output_port = 0;        // UDP端口
    };
    
    std::vector<SensorConfig> parseGroup(const std::string& file_path, 
                                       const std::string& group_name);
    
private:
    bool isLegacyFormat(const std::string& line);
    SensorConfig parseLegacyLine(const std::string& line);
    SensorConfig parseNewFormatLine(const std::string& line);
    std::string trim(const std::string& str);
    std::vector<std::string> split(const std::string& str, char delimiter);
};

// 多传感器管理器
class SimpleMultiSensorManager {
private:
    std::vector<std::unique_ptr<SensorBase>> sensors_;
    int64_t earliest_timestamp_ = INT64_MAX;
    bool is_loop_ = false;
    bool is_debug_ = false;
    uint16_t camera_type_ = 0;
    bool running_ = false;
    
public:
    SimpleMultiSensorManager(bool is_loop = false, bool is_debug = false, 
                           uint16_t camera_type = 0);
    
    bool loadConfiguration(const std::string& config_file, const std::string& group_name);
    bool initializeAllSensors();
    void startPlayback();
    void stopPlayback();
    
private:
    std::unique_ptr<SensorBase> createSensor(const SimpleConfigParser::SensorConfig& config);
    void findEarliestTimestamp();
    int64_t get_timestamp_ms();
    void playbackLoop();
    bool shouldProcessSensor(SensorBase* sensor, int64_t current_time, int64_t time_interval);
    void resetAllSensors();
};

#endif // MULTI_SENSOR_SERVER_H
