# RTSP 工具集

这是一个用于处理 RTSP 视频流的工具集，主要包含两个工具：

1. RTSP 服务器工具 (`rtsp_tool`)
2. RTSP 时间戳处理工具 (`rtsp_timestamp_proc_tool`)

## 文件列表

### RTSP 推流工具相关文件
- `rtsp_server.cpp` - 主程序源文件
- `rtsp_server.h` - 头文件，包含类定义和数据结构
- `rtspconfig.txt` - 配置文件示例（INI格式）
- `build.sh` - 自动化编译脚本

### RTSP 时间戳处理工具相关文件
- `rtsp_timestamp_proc_tool.cpp` - 时间戳提取工具源文件

## 功能特点

### RTSP 服务器工具
- **支持多视频组管理** - 通过INI配置文件管理不同的视频组
- **灵活的组选择** - 通过命令行参数指定要播放的视频组
- 支持多路视频流同时推流
- 支持循环播放模式
- 支持自定义时间戳注入
- 支持海康和大华摄像头的 SEI 帧格式
- 支持时间戳对齐和误差修正
- 支持调试模式输出详细信息
- **跨平台兼容** - 自动处理Windows/Linux换行符差异

### RTSP 时间戳处理工具
- 从视频流中提取 SEI 时间戳信息
- 将时间戳保存为文本文件
- 支持自定义输出文件路径

## 编译方式

### 方法一：使用编译脚本（推荐）

项目提供了自动化编译脚本 `build.sh`，会自动检测FFmpeg库配置：

```sh
# 赋予执行权限
chmod +x build.sh

# 运行编译脚本
./build.sh
```

编译脚本功能：
- 自动使用 `pkg-config` 检测FFmpeg库路径和参数
- 使用C++11标准编译
- 生成可执行文件 `rtsp_tool_2204`
- 显示编译结果和文件大小
- 编译失败时返回错误代码

### 方法二：手动编译

```sh
# 编译 RTSP 推流工具
g++ -o rtsp_tool  rtsp_server.cpp -I/usr/local/include -L/usr/local/lib -lavformat -lavcodec -lavutil -lz -lpthread -lswresample

# 编译时间戳处理工具
g++ -o rtsp_timestamp_proc_tool rtsp_timestamp_proc_tool.cpp -I/usr/local/include -L/usr/local/lib -lavformat -lavcodec -lavutil -lz
```

### 编译要求

- 支持C++11的编译器（GCC 4.8+）
- 已安装FFmpeg开发库
- 系统支持 `pkg-config`（推荐使用编译脚本时）

## 使用方法

### RTSP 服务器工具

```sh
./rtsp_tool -f <配置文件> -g <视频组名称> [-l] [-d] [-t <类型>] [-h]

选项说明：
  -f <配置文件>  必须指定的INI格式配置文件
  -g <组名称>    必须指定要播放的视频组名称
  -d            输出调试信息
  -l            循环播放模式
  -h            显示帮助信息
  -t <类型>     设置 SEI 帧类型 (0: 默认, 1: 大华, 2: 海康)
```

#### 配置文件格式 (INI格式)

```ini
# RTSP流配置文件 - INI格式
# 支持多个视频组，使用 -g 参数指定要播放的组

[deqing-001]
v_10.5.200.218_1723169047302.ts,v_10.5.200.218_1723169047302.txt,rtsp://root:root@192.168.50.233:8554/stream1
v_10.5.200.223_1723169047327.ts,v_10.5.200.223_1723169047327.txt,rtsp://root:root@192.168.50.233:8554/stream2

[deqing-002]
v_10.5.200.229_1723169047331.ts,v_10.5.200.229_1723169047331.txt,rtsp://root:root@192.168.50.233:8554/stream3
v_10.5.200.238_1723169047332.ts,v_10.5.200.238_1723169047332.txt,rtsp://root:root@192.168.50.233:8554/stream4
```

#### 配置文件说明
- **组标识**：`[组名称]` 格式定义视频组
- **流配置**：每行包含3个字段，用逗号分隔
  - 字段1：输入视频文件路径（.ts格式）
  - 字段2：时间戳文件路径（.txt格式）
  - 字段3：RTSP输出地址
- **注释**：以 `#` 开头的行为注释
- **空行**：支持空行用于格式美化

#### 使用示例

```sh
# 使用build.sh编译后的可执行文件
# 播放 deqing-001 组的视频流（循环播放，输出调试信息）
./rtsp_tool_2204 -f rtspconfig.txt -g deqing-001 -l -d

# 播放 deqing-002 组的视频流（添加海康SEI帧）
./rtsp_tool_2204 -f rtspconfig.txt -g deqing-002 -t 2

# 播放 deqing-001 组的视频流（单次播放，大华SEI帧格式）
./rtsp_tool_2204 -f rtspconfig.txt -g deqing-001 -t 1

# 或者使用手动编译的可执行文件
./rtsp_tool -f rtspconfig.txt -g deqing-001 -l -d
```

### RTSP 时间戳处理工具

```sh
./rtsp_timestamp_proc_tool <输入视频文件> [输出时间戳文件]

参数说明：
  输入视频文件：必须指定的输入视频文件路径
  输出时间戳文件：可选，默认与输入文件同名，后缀为 .txt
```

## 依赖项

- FFmpeg 库
  - libavformat
  - libavcodec
  - libavutil
  - libswresample

## FFmpeg 开发环境安装

### Ubuntu / Debian

```sh
sudo apt-get update
sudo apt-get install -y libavformat-dev libavcodec-dev libavutil-dev libswresample-dev pkg-config
```

### CentOS / RHEL

```sh
sudo yum install -y ffmpeg-devel
```

### macOS (使用 Homebrew)

```sh
brew install ffmpeg pkg-config
```

## 注意事项

1. 确保系统已安装 FFmpeg 开发库
2. 运行时需要确保有足够的系统权限
3. 配置文件中的路径需要使用绝对路径或相对于执行目录的路径
4. 时间戳文件格式为每行一个时间戳（毫秒）
5. **新版本要求必须指定视频组名称**，不再兼容旧的CSV格式
6. 配置文件支持跨平台使用（自动处理Windows/Linux换行符）
7. **推荐使用编译脚本** - 自动检测系统FFmpeg库配置

## 版本更新

### v2.0 (当前版本)
- 🎯 **新增INI配置格式** - 支持多视频组管理
- 🔧 **新增-g参数** - 必须指定视频组名称
- 🌐 **跨平台兼容** - 自动处理不同操作系统的换行符
- 📝 **更友好的错误提示** - 详细的配置文件格式说明
- ⚡ **零外部依赖** - 使用标准C++库解析INI格式
- 🛠️ **新增编译脚本** - 提供自动化编译工具