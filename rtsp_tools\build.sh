#!/bin/bash

# 编译RTSP服务器
echo "开始编译RTSP服务器..."

# 获取FFmpeg的编译参数
CFLAGS=$(pkg-config --cflags libavformat libavcodec libavutil libswresample)
LIBS=$(pkg-config --libs libavformat libavcodec libavutil libswresample)

# 编译命令
g++ -std=c++11 -o rtsp_tool_2204 rtsp_server.cpp $CFLAGS $LIBS -lpthread

if [ $? -eq 0 ]; then
    echo "编译成功！生成可执行文件: rtsp_tool_2204"
    echo "文件大小:"
    ls -lh rtsp_tool_2204
else
    echo "编译失败！"
    exit 1
fi