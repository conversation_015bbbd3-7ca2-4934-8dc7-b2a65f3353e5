@echo off
echo ===== Multi-Sensor Test Data Generator =====
echo Generating test JSON data files...

REM 生成测试雷达数据
echo [> test_radar.json
echo   {>> test_radar.json
echo     "timestamp": 1723169047000,>> test_radar.json
echo     "data": {>> test_radar.json
echo       "sensor_id": "radar_001",>> test_radar.json
echo       "detection_count": 3,>> test_radar.json
echo       "detections": [>> test_radar.json
echo         {"range": 25.6, "angle": 15.2, "velocity": 12.3},>> test_radar.json
echo         {"range": 30.1, "angle": 45.8, "velocity": 8.7},>> test_radar.json
echo         {"range": 18.9, "angle": -12.5, "velocity": 15.2}>> test_radar.json
echo       ]>> test_radar.json
echo     }>> test_radar.json
echo   },>> test_radar.json
echo   {>> test_radar.json
echo     "timestamp": 1723169047040,>> test_radar.json
echo     "data": {>> test_radar.json
echo       "sensor_id": "radar_001",>> test_radar.json
echo       "detection_count": 2,>> test_radar.json
echo       "detections": [>> test_radar.json
echo         {"range": 26.1, "angle": 16.0, "velocity": 12.1},>> test_radar.json
echo         {"range": 29.8, "angle": 44.2, "velocity": 9.1}>> test_radar.json
echo       ]>> test_radar.json
echo     }>> test_radar.json
echo   },>> test_radar.json
echo   {>> test_radar.json
echo     "timestamp": 1723169047080,>> test_radar.json
echo     "data": {>> test_radar.json
echo       "sensor_id": "radar_001",>> test_radar.json
echo       "detection_count": 1,>> test_radar.json
echo       "detections": [>> test_radar.json
echo         {"range": 26.5, "angle": 16.8, "velocity": 11.9}>> test_radar.json
echo       ]>> test_radar.json
echo     }>> test_radar.json
echo   }>> test_radar.json
echo ]>> test_radar.json

REM 生成测试激光雷达数据
echo [> test_lidar.json
echo   {>> test_lidar.json
echo     "timestamp": 1723169047000,>> test_lidar.json
echo     "data": {>> test_lidar.json
echo       "sensor_id": "lidar_001",>> test_lidar.json
echo       "point_count": 1024,>> test_lidar.json
echo       "scan_frequency": 10.0,>> test_lidar.json
echo       "points": [>> test_lidar.json
echo         {"x": 1.5, "y": 2.3, "z": 0.1, "intensity": 120},>> test_lidar.json
echo         {"x": 2.1, "y": 1.8, "z": 0.2, "intensity": 95},>> test_lidar.json
echo         {"x": 0.8, "y": 3.2, "z": 0.0, "intensity": 150}>> test_lidar.json
echo       ]>> test_lidar.json
echo     }>> test_lidar.json
echo   },>> test_lidar.json
echo   {>> test_lidar.json
echo     "timestamp": 1723169047050,>> test_lidar.json
echo     "data": {>> test_lidar.json
echo       "sensor_id": "lidar_001",>> test_lidar.json
echo       "point_count": 1056,>> test_lidar.json
echo       "scan_frequency": 10.0,>> test_lidar.json
echo       "points": [>> test_lidar.json
echo         {"x": 1.6, "y": 2.4, "z": 0.1, "intensity": 118},>> test_lidar.json
echo         {"x": 2.2, "y": 1.9, "z": 0.2, "intensity": 97},>> test_lidar.json
echo         {"x": 0.9, "y": 3.3, "z": 0.0, "intensity": 148}>> test_lidar.json
echo       ]>> test_lidar.json
echo     }>> test_lidar.json
echo   },>> test_lidar.json
echo   {>> test_lidar.json
echo     "timestamp": 1723169047100,>> test_lidar.json
echo     "data": {>> test_lidar.json
echo       "sensor_id": "lidar_001",>> test_lidar.json
echo       "point_count": 1089,>> test_lidar.json
echo       "scan_frequency": 10.0,>> test_lidar.json
echo       "points": [>> test_lidar.json
echo         {"x": 1.7, "y": 2.5, "z": 0.1, "intensity": 116},>> test_lidar.json
echo         {"x": 2.3, "y": 2.0, "z": 0.2, "intensity": 99},>> test_lidar.json
echo         {"x": 1.0, "y": 3.4, "z": 0.0, "intensity": 146}>> test_lidar.json
echo       ]>> test_lidar.json
echo     }>> test_lidar.json
echo   }>> test_lidar.json
echo ]>> test_lidar.json

echo.
echo ===== Test Data Generated Successfully =====
echo Files created:
echo   - test_radar.json (Radar sensor test data)
echo   - test_lidar.json (Lidar sensor test data)
echo.
echo You can now test the multi-sensor tool with:
echo   multi_sensor_tool.exe -f test_config.txt -g json-only-test -d
echo.
echo To monitor UDP output, use PowerShell:
echo   powershell -Command "$udp = New-Object System.Net.Sockets.UdpClient(9001); while($true) { $ep = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Any, 0); $data = $udp.Receive([ref]$ep); [System.Text.Encoding]::UTF8.GetString($data) }"
echo.
pause
