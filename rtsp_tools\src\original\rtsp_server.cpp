#include "rtsp_server.h"

/**
 * RtspServer构造函数 - 初始化RTSP服务器
 * 
 * 主要工作：
 * 1. 保存配置参数（流列表、循环播放、调试模式、摄像头类型）
 * 2. 读取每个流的时间戳文件
 * 3. 找到所有流中最早的时间戳，用于多流时间对齐
 * 
 * @param streams 流配置列表
 * @param is_loop 是否循环播放
 * @param is_debug 是否输出调试信息
 * @param camera_type 摄像头类型（用于SEI帧格式）
 */
RtspServer::RtspServer(std::vector<StreamContext> &streams, bool is_loop, bool is_debug, uint16_t camera_type)
    : streams_(streams), is_loop_(is_loop), is_debug_(is_debug), camera_type_(camera_type)
{
  // 遍历所有流，读取时间戳文件并找到最早时间戳
  for (auto &stream : streams_)
  {
    // 读取当前流的时间戳文件到内存
    stream.time_base = readFileToInt64(stream.input_file_txt);
    if (stream.time_base.empty())
    {
      std::cerr << "无法读取时间戳文件: " << stream.input_file_txt << std::endl;
      return;
    }
    
    // 更新全局最早时间戳（用于多流同步）
    if (stream.time_base[0] < earliest_timestamp_)
    {
      earliest_timestamp_ = stream.time_base[0];
    }
  }
  std::cout << "earliest_timestamp: " << earliest_timestamp_ << std::endl;
}

/**
 * RtspServer析构函数 - 清理所有资源
 * 
 * 清理工作：
 * 1. 写入流的结束标记
 * 2. 关闭网络连接
 * 3. 释放FFmpeg上下文
 * 4. 关闭输入文件
 */
RtspServer::~RtspServer() {
    for (auto &stream : streams_) {
        if (stream.output_ctx) {
            // 写入流结束标记
            av_write_trailer(stream.output_ctx);
            
            // 如果不是无文件格式，关闭网络连接
            if (stream.output_ctx && !(stream.output_ctx->oformat->flags & AVFMT_NOFILE)) {
                avio_closep(&stream.output_ctx->pb);
            }
            
            // 释放输出上下文
            avformat_free_context(stream.output_ctx);
        }
        
        if (stream.input_ctx) {
            // 关闭输入文件
            avformat_close_input(&stream.input_ctx);
        }
    }
}

/**
 * 获取当前系统时间戳（毫秒）
 * 
 * @return 当前时间的毫秒时间戳
 */
int64_t RtspServer::get_timestamp_ms() {
    auto now = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch());
    return duration.count();
}

/**
 * 读取时间戳文件到内存
 * 
 * 文件格式：每行一个时间戳（毫秒）
 * 支持空行和注释（以#开头）
 * 
 * @param filePath 时间戳文件路径
 * @return 时间戳数组
 */
std::vector<int64_t> RtspServer::readFileToInt64(const std::string& filePath) {
    std::ifstream file(filePath);
    if (!file.is_open()) {
        throw std::runtime_error("无法打开文件: " + filePath);
    }

    std::vector<int64_t> numbers;
    std::string line;
    std::size_t lineNumber = 0;

    while (std::getline(file, line)) {
        ++lineNumber;
        
        // 去除行首尾的空白字符
        auto start = std::min(line.find_first_not_of(" \t\r\n"), line.size());
        std::string trimmedLine = (start == std::string::npos) ? "" : line.substr(start);

        // 跳过空行
        if (trimmedLine.empty()) {
            continue;
        }

        // 尝试解析数字
        std::istringstream iss(trimmedLine);
        int64_t num;
        if (!(iss >> num)) {
            std::cerr << "格式错误行 " << lineNumber << ": " << trimmedLine << std::endl;
        } else {
            numbers.push_back(num);
        }
    }

    return numbers;
}

/**
 * 初始化单个视频流
 * 
 * 初始化步骤：
 * 1. 打开输入文件（TS格式）
 * 2. 获取流信息（编解码器、帧率等）
 * 3. 创建输出上下文（RTSP格式）
 * 4. 复制流参数
 * 5. 建立网络连接
 * 6. 写入流头部信息
 * 
 * @param stream 要初始化的流上下文
 * @return 0成功，-1失败
 */
int RtspServer::init_stream(StreamContext &stream) {
    // 1. 打开输入文件
    if (avformat_open_input(&stream.input_ctx, stream.input_file_ts.c_str(), nullptr, nullptr) < 0) {
        std::cerr << "无法打开输入文件: " << stream.input_file_ts << std::endl;
        return -1;
    }
    
    // 2. 获取流信息
    if (avformat_find_stream_info(stream.input_ctx, nullptr) < 0) {
        std::cerr << "无法获取流信息: " << stream.input_file_ts << std::endl;
        avformat_close_input(&stream.input_ctx);
        return -1;
    }

    // 3. 创建输出上下文（RTSP格式）
    if (avformat_alloc_output_context2(&stream.output_ctx, nullptr, "rtsp", stream.output_url.c_str()) < 0) {
        std::cerr << "创建输出上下文失败: " << stream.output_url << std::endl;
        avformat_close_input(&stream.input_ctx);
        return -1;
    }

    // 4. 复制所有流的参数（视频、音频等）
    for (unsigned i = 0; i < stream.input_ctx->nb_streams; i++) {
        AVStream *out_stream = avformat_new_stream(stream.output_ctx, nullptr);
        AVStream *in_stream = stream.input_ctx->streams[i];
        
        // 复制编解码器参数
        avcodec_parameters_copy(out_stream->codecpar, in_stream->codecpar);
        out_stream->time_base = in_stream->time_base;

        // 如果是视频流，计算帧间隔
        if (in_stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            AVRational avg_frame_rate = in_stream->avg_frame_rate;
            double fps = av_q2d(avg_frame_rate);
            // 计算帧间隔（90kHz时间基）
            stream.average_interval = (1000 / fps) * 90;
            
            if (is_debug_) {
                std::cout << "file: " << stream.input_file_ts << ", fps: " << fps 
                          << ", stream.average_interval: " << stream.average_interval << std::endl;
            }
        }
    }

    // 5. 建立网络连接（如果需要）
    if (!(stream.output_ctx->oformat->flags & AVFMT_NOFILE)) {
        if (avio_open(&stream.output_ctx->pb, stream.output_url.c_str(), AVIO_FLAG_WRITE) < 0) {
            std::cerr << "无法打开RTSP地址: " << stream.output_url << std::endl;
            avformat_free_context(stream.output_ctx);
            avformat_close_input(&stream.input_ctx);
            return -1;
        }
    }

    // 6. 写入流头部信息
    if (avformat_write_header(stream.output_ctx, nullptr) < 0) {
        std::cerr << "写入头部失败: " << stream.output_url << std::endl;
        return -1;
    }

    return 0;
}

/**
 * 重新加载流 - 用于循环播放或错误恢复
 * 
 * 重载步骤：
 * 1. 清理当前数据包
 * 2. 重置时间戳记录
 * 3. 关闭当前连接
 * 4. 重新初始化流
 * 
 * @param stream 要重新加载的流
 */
void RtspServer::reload_stream(StreamContext &stream) {
    // 1. 清理当前数据包
    av_packet_unref(&stream.pkt);
    
    // 2. 重置时间戳记录
    stream.last_dts[stream.pkt.stream_index] = 0;
    
    // 3. 关闭当前连接和上下文
    avformat_close_input(&stream.input_ctx);
    avformat_free_context(stream.input_ctx);
    avformat_free_context(stream.output_ctx);

    // 4. 重新初始化
    if (init_stream(stream) < 0) {
        std::cerr << "无法重新初始化流: " << stream.input_file_ts << std::endl;
    }
}

/**
 * 处理时间戳 - 确保时间戳的连续性和正确性
 * 
 * 处理逻辑：
 * 1. 循环播放模式：使用固定间隔计算时间戳
 * 2. 防止时间戳倒退
 * 3. 时间基转换（输入时间基 -> 输出时间基）
 * 
 * @param stream 要处理的流上下文
 */
void RtspServer::handle_timestamps(StreamContext &stream) {
    AVStream *in_stream = stream.input_ctx->streams[stream.pkt.stream_index];
    AVStream *out_stream = stream.output_ctx->streams[stream.pkt.stream_index];

    // 循环播放模式：使用固定间隔避免时间戳跳跃
    if (is_loop_) {
        if (stream.last_dts[stream.pkt.stream_index] == 0) {
            // 首帧：使用原始时间戳
            stream.last_dts[stream.pkt.stream_index] = stream.pkt.dts;
        } else {
            // 后续帧：使用固定间隔递增
            stream.pkt.dts = stream.last_dts[stream.pkt.stream_index] + stream.average_interval;
        }
    }

    // 设置PTS等于DTS（简化处理）
    stream.pkt.pts = stream.pkt.dts;

    // 防止时间戳倒退
    if (stream.pkt.dts <= stream.last_dts[stream.pkt.stream_index]) {
        stream.pkt.dts = stream.last_dts[stream.pkt.stream_index] + 1;
        stream.pkt.pts = stream.pkt.dts;
    }

    // 更新最后时间戳记录
    stream.last_dts[stream.pkt.stream_index] = stream.pkt.dts;
    
    // 时间基转换：输入时间基 -> 输出时间基
    av_packet_rescale_ts(&stream.pkt, in_stream->time_base, out_stream->time_base);
}

/**
 * 重置所有流到文件开头 - 用于循环播放
 * 
 * 重置操作：
 * 1. 将文件指针移动到开头
 * 2. 重置帧序号计数器
 */
void RtspServer::reset_all_stream() {
    for (auto &stream : streams_) {
        // 定位到文件开头
        av_seek_frame(stream.input_ctx, -1, 0, AVSEEK_FLAG_BACKWARD);
        // 重置帧计数器
        stream.frame_seq = 0;
    }
}

/**
 * 创建SEI帧 - 生成包含时间戳的SEI帧
 * 
 * SEI（Supplemental Enhancement Information）帧用于携带辅助信息
 * 不同厂商有不同的SEI帧格式：
 * - 海康威视：使用"HikTechnologyxy"作为UUID
 * - 大华：使用"DahuaTechnology"作为UUID
 * 
 * SEI帧结构：
 * [起始码][NAL类型][负载类型][负载大小][UUID][时间戳][保留字段][停止位]
 * 
 * @param out_size 输出SEI帧的大小
 * @param stream 流上下文，用于获取当前时间戳
 * @return 生成的SEI帧数据指针
 */
uint8_t* RtspServer::create_sei_frame(size_t *out_size, const StreamContext &stream) {
  // 从时间戳数组中获取当前帧的时间戳
  int64_t timestamp_ms = stream.time_base[stream.frame_seq];

  // 海康威视SEI帧格式
  if (camera_type_ == 2)
  {
    // SEI帧固定字段
    const uint8_t start_code[] = {0x00, 0x00, 0x00, 0x01};  // H.264起始码
    const uint8_t nal_type = 0x06;        // NAL单元类型：SEI帧
    const uint8_t payload_type = 0x05;    // 负载类型：用户数据
    const uint8_t payload_size = 0x21;    // 负载大小：33字节
    const char uuid[] = "HikTechnologyxy\0"; // 海康威视UUID标识
    char timestamp[14];                   // 时间戳字符串（13位数字+结束符）

    // 格式化时间戳为13位字符串
    snprintf(timestamp, sizeof(timestamp), "%013ld", timestamp_ms);
    timestamp[13] = '\0';
    
    // 保留字段（填充数据）
    const uint8_t reserved[] = {0x5A, 0x5A, 0x5A};
    const uint8_t rbsp_stop_bit = 0x80;   // RBSP停止位

    // 计算各部分长度
    size_t uuid_len = 16;
    size_t timestamp_len = 14;
    size_t reserved_len = 3;

    // 计算总长度：起始码(4) + NAL类型(1) + 负载类型(1) + 负载大小(1) + 数据(33) + 停止位(1)
    *out_size = 4 + 3 + 33 + 1;

    // 分配内存
    uint8_t *sei = (uint8_t *)av_malloc(*out_size);
    if (!sei)
    {
      std::cerr << "Failed to allocate SEI frame" << std::endl;
      return nullptr;
    }

    // 组装SEI帧数据
    size_t pos = 0;
    memcpy(sei + pos, start_code, 4);     // 起始码
    pos += 4;
    sei[pos++] = nal_type;                // NAL类型
    sei[pos++] = payload_type;            // 负载类型
    sei[pos++] = payload_size;            // 负载大小
    memcpy(sei + pos, uuid, uuid_len);    // UUID
    pos += uuid_len;
    memcpy(sei + pos, timestamp, timestamp_len); // 时间戳
    pos += timestamp_len;
    memcpy(sei + pos, reserved, reserved_len);   // 保留字段
    pos += reserved_len;
    sei[pos++] = rbsp_stop_bit;           // 停止位

    // 调试输出
    if (is_debug_)
    {
      std::cout << "New SEI Frame Timestamp: " << timestamp << std::endl;
      std::cout << "New SEI Frame Length: " << *out_size << " bytes" << std::endl;
      for (size_t i = 0; i < *out_size; i++)
      {
        std::cout << std::hex << (int)sei[i] << " ";
      }
      std::cout << std::dec << std::endl;
    }
    return sei;
  }
  // 大华SEI帧格式
  else if (camera_type_ == 1)
  {
    // 大华SEI帧字段（与海康类似但有差异）
    const uint8_t start_code[] = {0x00, 0x00, 0x00, 0x01};
    const uint8_t nal_type = 0x06;
    const uint8_t payload_type1 = 0x05;   // 第一个负载类型
    const uint8_t payload_type2 = 0x00;   // 第二个负载类型（大华特有）
    const uint8_t payload_size = 0x30;    // 负载大小：48字节
    const char uuid[] = "DahuaTechnology\0"; // 大华UUID标识
    char timestamp[14];

    snprintf(timestamp, sizeof(timestamp), "%013ld", timestamp_ms);
    timestamp[13] = '\0';
    
    // 大华的保留字段更长（18字节）
    const uint8_t reserved[] = {0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A,
                                0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A, 0x5A};
    const uint8_t rbsp_stop_bit = 0x80;

    size_t uuid_len = 16;
    size_t timestamp_len = 14;
    size_t reserved_len = 18;

    // 大华SEI帧总长度：起始码(4) + 头部(4) + 数据(48) + 停止位(1)
    *out_size = 4 + 4 + 48 + 1;

    uint8_t *sei = (uint8_t *)av_malloc(*out_size);
    if (!sei)
    {
      std::cerr << "Failed to allocate SEI frame" << std::endl;
      return nullptr;
    }

    // 组装大华SEI帧
    size_t pos = 0;
    memcpy(sei + pos, start_code, 4);
    pos += 4;
    sei[pos++] = nal_type;
    sei[pos++] = payload_type1;
    sei[pos++] = payload_type2;           // 大华特有的第二个负载类型
    sei[pos++] = payload_size;
    memcpy(sei + pos, uuid, uuid_len);
    pos += uuid_len;
    memcpy(sei + pos, timestamp, timestamp_len);
    pos += timestamp_len;
    memcpy(sei + pos, reserved, reserved_len);
    pos += reserved_len;
    sei[pos++] = rbsp_stop_bit;

    if (is_debug_)
    {
      std::cout << "New SEI Frame Timestamp: " << timestamp << std::endl;
      std::cout << "New SEI Frame Length: " << *out_size << " bytes" << std::endl;
      for (size_t i = 0; i < *out_size; i++)
      {
        std::cout << std::hex << (int)sei[i] << " ";
      }
      std::cout << std::dec << std::endl;
    }
    return sei;
  }
  
  // camera_type == 0：不添加SEI帧
  return nullptr;
}

/**
 * 处理数据包 - 移除原始SEI帧并添加新SEI帧
 * 
 * 处理步骤：
 * 1. 扫描H.264数据包，找到所有NAL单元
 * 2. 识别并移除原有的SEI帧（NAL类型=6）
 * 3. 创建新的SEI帧
 * 4. 将新SEI帧插入到数据包开头
 * 5. 重新组装数据包
 * 
 * H.264 NAL单元格式：
 * - 3字节起始码：0x00 0x00 0x01
 * - 4字节起始码：0x00 0x00 0x00 0x01
 * - NAL头部：1字节（包含NAL类型）
 * 
 * @param pkt 要处理的数据包
 * @param stream 对应的流上下文
 */
void RtspServer::process_packet(AVPacket *pkt, StreamContext &stream) {
    // 如果不需要处理SEI帧，直接返回
    if (camera_type_ == 0){
      return;
    }

    // 数据包有效性检查
    if (!pkt || !pkt->data || pkt->size < 3) {
        std::cerr << "Invalid packet or empty data" << std::endl;
        return;
    }

    uint8_t *src = pkt->data;
    size_t size = pkt->size;
    
    // 创建临时缓冲区存储处理后的数据
    uint8_t *temp_data = (uint8_t *)av_malloc(size);
    if (!temp_data) {
        std::cerr << "Failed to allocate temporary buffer" << std::endl;
        return;
    }

    size_t temp_size = 0;
    size_t pos = 0;

    // 扫描整个数据包，寻找NAL单元
    while (pos < size) {
        // 寻找3字节起始码：0x00 0x00 0x01
        if (pos + 3 < size && src[pos] == 0x00 && src[pos + 1] == 0x00 && src[pos + 2] == 0x01) {
            uint8_t nal_type = src[pos + 3] & 0x1F;  // 提取NAL类型（低5位）
            size_t start_code_len = 3;

            // 如果是SEI帧（NAL类型=6），跳过它
            if (nal_type == 6) {
              if (is_debug_)
              {
                std::cout << "检测到原始 SEI 帧，已移除 (pos=" << pos << ")" << std::endl;
              }
              
              // 跳过起始码，寻找下一个NAL单元
              pos += start_code_len;
              while (pos < size)
              {
                // 寻找下一个起始码
                if (pos + 2 < size && src[pos] == 0x00 && src[pos + 1] == 0x00 &&
                    (src[pos + 2] == 0x01 || (pos + 3 < size && src[pos + 2] == 0x00 && src[pos + 3] == 0x01)))
                {
                  break;
                }
                pos++;
              }
              continue;  // 跳过这个SEI帧
            }

            // 复制非SEI帧的NAL单元
            memcpy(temp_data + temp_size, src + pos, start_code_len + 1);
            temp_size += start_code_len + 1;
            pos += start_code_len + 1;
        }
        // 寻找4字节起始码：0x00 0x00 0x00 0x01
        else if (pos + 4 < size && src[pos] == 0x00 && src[pos + 1] == 0x00 && 
                 src[pos + 2] == 0x00 && src[pos + 3] == 0x01) {
            uint8_t nal_type = src[pos + 4] & 0x1F;
            size_t start_code_len = 4;

            // 处理4字节起始码的SEI帧
            if (nal_type == 6) {
                if (is_debug_) {
                    std::cout << "检测到原始 SEI 帧，已移除 (pos=" << pos << ")" << std::endl;
                }
                pos += start_code_len;
                while (pos < size) {
                    if (pos + 2 < size && src[pos] == 0x00 && src[pos + 1] == 0x00 && 
                        (src[pos + 2] == 0x01 || (pos + 3 < size && src[pos + 2] == 0x00 && src[pos + 3] == 0x01))) {
                        break;
                    }
                    pos++;
                }
                continue;
            }

            // 复制非SEI帧的NAL单元
            memcpy(temp_data + temp_size, src + pos, start_code_len + 1);
            temp_size += start_code_len + 1;
            pos += start_code_len + 1;
        }
        else {
            // 复制普通数据字节
            temp_data[temp_size++] = src[pos++];
        }
    }

    // 创建新的SEI帧
    size_t sei_size = 0;
    uint8_t *sei_data = create_sei_frame(&sei_size, stream);
    if (!sei_data) {
        av_free(temp_data);
        return;
    }

    // 重新组装数据包：新SEI帧 + 原始数据（已移除旧SEI帧）
    size_t new_size = sei_size + temp_size;
    uint8_t *new_data = (uint8_t *)av_realloc(pkt->data, new_size);
    if (!new_data) {
        std::cerr << "Failed to realloc packet data" << std::endl;
        // 重新分配失败，尝试新分配
        new_data = (uint8_t *)av_malloc(new_size);
        if (!new_data) {
            av_free(sei_data);
            av_free(temp_data);
            return;
        }
    }

    // 复制数据：先放SEI帧，再放原始数据
    memcpy(new_data, sei_data, sei_size);                    // 新SEI帧
    memcpy(new_data + sei_size, temp_data, temp_size);       // 原始数据

    // 更新数据包
    pkt->data = new_data;
    pkt->size = new_size;

    // 清理临时内存
    av_free(sei_data);
    av_free(temp_data);
}

/**
 * 主推流循环 - 核心方法
 * 
 * 主要流程：
 * 1. 初始化FFmpeg和所有流
 * 2. 计算时间偏移量
 * 3. 进入主循环：
 *    - 检查是否到达播放时间
 *    - 读取视频帧
 *    - 处理SEI帧
 *    - 处理时间戳
 *    - 推送到RTSP服务器
 *    - 处理循环播放和错误重试
 */
void RtspServer::push_stream_loop() {
    // 设置FFmpeg日志级别
    if (is_debug_) {
        av_log_set_level(AV_LOG_DEBUG);
    } else {
        av_log_set_level(AV_LOG_FATAL);
    }
    
    // 初始化FFmpeg网络组件
    avformat_network_init();

    // 初始化所有流
    for (auto &stream : streams_) {
        if (init_stream(stream) < 0) {
            return;
        }
    }

    // 计算时间偏移量（当前时间 - 最早时间戳）
    auto time_now = get_timestamp_ms();
    uint64_t time_interval = time_now - earliest_timestamp_;

    // 主推流循环
    while (true) {
        time_now = get_timestamp_ms();
        
        // 遍历所有流
        for (auto &stream : streams_) {
            // 检查是否播放完毕
            if (stream.frame_seq >= stream.time_base.size()) {
                if (!is_loop_) {
                    std::cout << "视频流结束, 退出程序!" << std::endl;
                    exit(0);
                }
                
                // 循环播放：重置所有流
                std::cout << "视频流结束, 重新加载所有的流。" << std::endl;
                reset_all_stream();
                time_interval = time_now - earliest_timestamp_;
                break;
            }

            // 检查是否到达播放时间
            // time_interval: 程序启动时的时间偏移
            // stream.time_base[stream.frame_seq]: 当前帧应该播放的时间戳
            // time_now: 当前实际时间
            if (time_interval + stream.time_base[stream.frame_seq] > time_now) {
                continue;  // 还没到播放时间，跳过
            }

            // 读取一帧数据
            int ret = av_read_frame(stream.input_ctx, &stream.pkt);

            if (ret == AVERROR_EOF) {
                // 文件结束处理
                if (!is_loop_) {
                    std::cout << "视频流结束, 退出程序!" << std::endl;
                    exit(0);
                }
                std::cout << "视频流结束, 重新加载所有的流。" << std::endl;
                reset_all_stream();
                time_interval = time_now - earliest_timestamp_;
                break;
            }
            else if (ret < 0) {
                // 读取错误处理
                if (++stream.retry_count > MAX_RETRIES) {
                    std::cerr << "超过最大重试次数: " << stream.input_file_ts << std::endl;
                    return;
                }
                // 等待1秒后重试
                std::this_thread::sleep_for(std::chrono::seconds(1));
            }
            else {
                // 成功读取帧
                // 如果是视频帧，处理SEI帧
                if (stream.input_ctx->streams[stream.pkt.stream_index]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
                    process_packet(&stream.pkt, stream);
                }

                // 处理时间戳
                handle_timestamps(stream);
                
                // 写入输出流（推送到RTSP）
                if (av_interleaved_write_frame(stream.output_ctx, &stream.pkt) < 0) {
                    std::cerr << "写入帧失败: " << stream.input_file_ts << std::endl;
                }
                
                // 释放数据包内存
                av_packet_unref(&stream.pkt);
                
                // 重置重试计数
                stream.retry_count = 0;
            }
            
            // 递增帧序号
            stream.frame_seq++;
        }
        
        // 短暂休眠，避免CPU占用过高
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
}

/**
 * 打印帮助信息
 */
void print_help(const char* program_name) {
    std::cout << "用法: " << program_name << " -f <配置文件> -g <视频组名称> [-l] [-d] [-t <类型>] [-h]\n"
              << "选项:\n"
              << "  -f <配置文件> 必须指定的INI格式配置文件\n"
              << "  -g <组名称>   必须指定要播放的视频组名称\n"
              << "  -d           输出debug信息\n"
              << "  -l           循环播放\n"
              << "  -h           显示帮助信息\n"
              << "  -t <类型>     增加sei帧的类型(0: 默认, 1: 大华, 2: 海康）\n"
              << "\n配置文件格式（INI）:\n"
              << "[group_name1]\n"
              << "video1.ts,timestamp1.txt,rtsp://url1\n"
              << "video2.ts,timestamp2.txt,rtsp://url2\n"
              << "\n[group_name2]\n"
              << "video3.ts,timestamp3.txt,rtsp://url3\n";
    exit(0);
}

/**
 * 字符串去除首尾空白字符（包括\r）
 */
std::string trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

/**
 * 加载指定组的流配置
 * 
 * INI配置文件格式：
 * [group_name1]
 * video1.ts,timestamp1.txt,rtsp://url1
 * video2.ts,timestamp2.txt,rtsp://url2
 * 
 * [group_name2]
 * video3.ts,timestamp3.txt,rtsp://url3
 * 
 * 支持注释行（#开头）和空行
 * 
 * @param config_path 配置文件路径
 * @param group_name 要加载的组名称
 * @param streams 输出的流配置列表
 * @return true成功，false失败
 */
bool load_stream_group(const std::string& config_path, const std::string& group_name, std::vector<StreamContext>& streams) {
    std::ifstream file(config_path);
    if (!file.is_open()) {
        std::cerr << "错误：无法打开配置文件 " << config_path << std::endl;
        return false;
    }

    std::string line;
    int line_num = 0;
    std::string current_group;
    bool found_group = false;
    bool in_target_group = false;
    
    while (std::getline(file, line)) {
        line_num++;
        line = trim(line);
        
        // 跳过空行和注释行
        if (line.empty() || line[0] == '#') continue;

        // 检查是否是组标识行 [group_name]
        if (line[0] == '[' && line.back() == ']') {
            current_group = line.substr(1, line.length() - 2);
            current_group = trim(current_group);
            
            if (current_group == group_name) {
                found_group = true;
                in_target_group = true;
            } else {
                in_target_group = false;
            }
            continue;
        }

        // 如果在目标组内，解析流配置
        if (in_target_group) {
            // 解析CSV格式：用逗号分割
            std::istringstream iss(line);
            std::string part;
            std::vector<std::string> parts;

            while (std::getline(iss, part, ',')) {
                parts.push_back(trim(part));
            }

            // 检查字段数量
            if (parts.size() != 3) {
                std::cerr << "配置错误（第 " << line_num << " 行）:每行需包含3个字段" << std::endl;
                return false;
            }
            
            // 创建流配置对象
            streams.emplace_back(parts[0], parts[1], parts[2]);
        }
    }

    if (!found_group) {
        std::cerr << "错误：未找到视频组 '" << group_name << "'" << std::endl;
        return false;
    }

    if (streams.empty()) {
        std::cerr << "错误：视频组 '" << group_name << "' 中未定义有效流" << std::endl;
        return false;
    }

    std::cout << "成功加载视频组 '" << group_name << "'，包含 " << streams.size() << " 个流" << std::endl;
    return true;
}

/**
 * 主函数 - 程序入口点
 * 
 * 功能：
 * 1. 解析命令行参数
 * 2. 加载指定组的配置
 * 3. 创建RTSP服务器实例
 * 4. 启动推流循环
 */
int main(int argc, char* argv[]) {
    // 初始化参数
    std::string config_file;
    std::string group_name;
    bool is_loop = false;
    bool is_debug = false;
    uint32_t camera_type = 0;

    // 解析命令行参数
    for (int i = 1; i < argc; ++i) {
        std::string arg = argv[i];
        if (arg == "-h") {
            print_help(argv[0]);
        }
        else if (arg == "-f") {
            if (i + 1 >= argc) {
                std::cerr << "错误：-f 后缺少配置文件名\n";
                exit(1);
            }
            config_file = argv[++i];
        }
        else if (arg == "-g") {
            if (i + 1 >= argc) {
                std::cerr << "错误：-g 后缺少视频组名称\n";
                exit(1);
            }
            group_name = argv[++i];
        }
        else if (arg == "-l") {
            is_loop = true;
        }
        else if (arg == "-d") {
            is_debug = true;
        }
        else if (arg == "-t")
        {
          if (i + 1 >= argc)
          {
            std::cerr << "错误：-t 后缺少类型值\n";
            exit(1);
          }
          camera_type = std::stoi(argv[++i]);
          if (camera_type > 2)
          {
            std::cerr << "错误：无效的相机类型 '" << camera_type << "'\n";
            exit(1);
          }
        }
        else {
            std::cerr << "错误：未知选项或参数 '" << arg << "'\n";
            exit(1);
        }
    }

    // 检查必需参数
    if (config_file.empty()) {
        std::cerr << "错误：必须指定 -f <配置文件>\n";
        print_help(argv[0]);
    }
    
    if (group_name.empty()) {
        std::cerr << "错误：必须指定 -g <视频组名称>\n";
        print_help(argv[0]);
    }

    try {
        // 加载指定组的流配置
        std::vector<StreamContext> streams;
        if (!load_stream_group(config_file, group_name, streams)) {
            exit(1);
        }
        
        // 创建并启动RTSP服务器
        RtspServer rtspServer(streams, is_loop, is_debug, camera_type);
        rtspServer.push_stream_loop();
    }
    catch (const std::exception &e) {
        std::cerr << "发生错误: " << e.what() << std::endl;
        return -1;
    }
    
    return 0;
}
