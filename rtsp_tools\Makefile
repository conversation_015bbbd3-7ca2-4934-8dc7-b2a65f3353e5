# Makefile for RTSP Tools Project
# RTSP工具项目主编译文件

# 编译器和标准
CXX = g++
CXXFLAGS = -std=c++11 -O2 -g -Wall -Wextra

# 目录结构
SRC_DIR = src
BUILD_DIR = build
BIN_DIR = $(BUILD_DIR)/bin
OBJ_DIR = $(BUILD_DIR)/obj
TEST_DIR = test

# 原始工具
ORIGINAL_SRC_DIR = $(SRC_DIR)/original
ORIGINAL_TARGET = $(BIN_DIR)/rtsp_tool
ORIGINAL_TIMESTAMP_TARGET = $(BIN_DIR)/rtsp_timestamp_proc_tool
ORIGINAL_SOURCES = $(ORIGINAL_SRC_DIR)/rtsp_server.cpp
ORIGINAL_TIMESTAMP_SOURCES = $(ORIGINAL_SRC_DIR)/rtsp_timestamp_proc_tool.cpp
ORIGINAL_HEADERS = $(ORIGINAL_SRC_DIR)/rtsp_server.h

# 多传感器工具
MULTI_SENSOR_SRC_DIR = $(SRC_DIR)/multi_sensor
MULTI_SENSOR_TARGET = $(BIN_DIR)/multi_sensor_tool
MULTI_SENSOR_SOURCES = $(MULTI_SENSOR_SRC_DIR)/multi_sensor_main.cpp $(MULTI_SENSOR_SRC_DIR)/multi_sensor_server.cpp
MULTI_SENSOR_HEADERS = $(MULTI_SENSOR_SRC_DIR)/multi_sensor_server.h

# 依赖库检测
FFMPEG_CFLAGS = $(shell pkg-config --cflags libavformat libavcodec libavutil 2>/dev/null)
FFMPEG_LIBS = $(shell pkg-config --libs libavformat libavcodec libavutil 2>/dev/null)

# JsonCpp库检测
JSONCPP_CFLAGS = $(shell pkg-config --cflags jsoncpp 2>/dev/null || echo "-I/usr/include -I/usr/local/include")
JSONCPP_LIBS = $(shell pkg-config --libs jsoncpp 2>/dev/null || echo "-ljsoncpp")

# 平台特定的网络库
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
    NETWORK_LIBS = 
endif
ifeq ($(UNAME_S),Darwin)
    NETWORK_LIBS = 
endif
ifneq (,$(findstring MINGW,$(UNAME_S)))
    NETWORK_LIBS = -lws2_32
endif
ifneq (,$(findstring CYGWIN,$(UNAME_S)))
    NETWORK_LIBS = -lws2_32
endif

# 所有编译标志
ALL_CFLAGS = $(CXXFLAGS) $(FFMPEG_CFLAGS) $(JSONCPP_CFLAGS)
ALL_LIBS = $(FFMPEG_LIBS) $(JSONCPP_LIBS) $(NETWORK_LIBS) -lpthread

# 默认目标
all: check-deps original multi-sensor

# 单独目标
original: check-deps $(ORIGINAL_TARGET) $(ORIGINAL_TIMESTAMP_TARGET)
multi-sensor: check-deps $(MULTI_SENSOR_TARGET)

# 检查依赖
check-deps:
	@echo "Checking dependencies..."
	@if ! pkg-config --exists libavformat libavcodec libavutil; then \
		echo "Error: FFmpeg development libraries not found."; \
		echo "Please install FFmpeg development packages:"; \
		echo "  Ubuntu/Debian: sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev"; \
		echo "  CentOS/RHEL: sudo yum install ffmpeg-devel"; \
		echo "  macOS: brew install ffmpeg"; \
		exit 1; \
	fi
	@if ! pkg-config --exists jsoncpp && ! [ -f /usr/include/json/json.h ] && ! [ -f /usr/local/include/json/json.h ]; then \
		echo "Warning: JsonCpp not found. Please install:"; \
		echo "  Ubuntu/Debian: sudo apt-get install libjsoncpp-dev"; \
		echo "  CentOS/RHEL: sudo yum install jsoncpp-devel"; \
		echo "  macOS: brew install jsoncpp"; \
	fi
	@echo "Dependencies check completed."

# 创建目录
$(BIN_DIR):
	@mkdir -p $(BIN_DIR) 2>/dev/null || mkdir $(BIN_DIR) 2>nul || true

$(OBJ_DIR):
	@mkdir -p $(OBJ_DIR) 2>/dev/null || mkdir $(OBJ_DIR) 2>nul || true

# 编译原始工具
$(ORIGINAL_TARGET): $(ORIGINAL_SOURCES) $(ORIGINAL_HEADERS) | $(BIN_DIR)
	@echo "Compiling original RTSP tool..."
	@echo "CFLAGS: $(ALL_CFLAGS)"
	@echo "LIBS: $(ALL_LIBS)"
	$(CXX) $(ALL_CFLAGS) -o $(ORIGINAL_TARGET) $(ORIGINAL_SOURCES) $(ALL_LIBS)
	@echo "Build successful: $(ORIGINAL_TARGET)"

# 编译时间戳处理工具
$(ORIGINAL_TIMESTAMP_TARGET): $(ORIGINAL_TIMESTAMP_SOURCES) | $(BIN_DIR)
	@echo "Compiling timestamp processing tool..."
	$(CXX) $(ALL_CFLAGS) -o $(ORIGINAL_TIMESTAMP_TARGET) $(ORIGINAL_TIMESTAMP_SOURCES) $(ALL_LIBS)
	@echo "Build successful: $(ORIGINAL_TIMESTAMP_TARGET)"

# 编译多传感器工具
$(MULTI_SENSOR_TARGET): $(MULTI_SENSOR_SOURCES) $(MULTI_SENSOR_HEADERS) | $(BIN_DIR)
	@echo "Compiling multi-sensor tool..."
	@echo "CFLAGS: $(ALL_CFLAGS)"
	@echo "LIBS: $(ALL_LIBS)"
	$(CXX) $(ALL_CFLAGS) -o $(MULTI_SENSOR_TARGET) $(MULTI_SENSOR_SOURCES) $(ALL_LIBS)
	@echo "Build successful: $(MULTI_SENSOR_TARGET)"

# 清理
clean:
	@echo "Cleaning up..."
	@rm -f $(BIN_DIR)/* 2>/dev/null || del /Q $(BIN_DIR)\* 2>nul || true
	@rm -f $(OBJ_DIR)/* 2>/dev/null || del /Q $(OBJ_DIR)\* 2>nul || true
	@rm -f *.o *.core 2>/dev/null || del /Q *.o *.core 2>nul || true
	@echo "Clean completed."

# 安装依赖（Ubuntu/Debian）
install-deps-ubuntu:
	@echo "Installing dependencies on Ubuntu/Debian..."
	sudo apt-get update
	sudo apt-get install -y libavformat-dev libavcodec-dev libavutil-dev libjsoncpp-dev pkg-config build-essential

# 安装依赖（CentOS/RHEL）
install-deps-centos:
	@echo "Installing dependencies on CentOS/RHEL..."
	sudo yum install -y ffmpeg-devel jsoncpp-devel pkgconfig gcc-c++

# 安装依赖（macOS）
install-deps-macos:
	@echo "Installing dependencies on macOS..."
	brew install ffmpeg jsoncpp pkg-config

# 生成测试数据
test-data:
	@echo "Generating test data..."
	@./test_multi_sensor.sh

# 运行测试
test: multi-sensor test-data
	@echo "Running tests..."
	@chmod +x $(TEST_DIR)/scripts/test_multi_sensor.sh 2>/dev/null || true
	@cd $(TEST_DIR)/scripts && ./test_multi_sensor.sh

# 快速测试（仅JSON传感器）
quick-test: multi-sensor
	@echo "Running quick test..."
	@echo "Using existing test data in $(TEST_DIR)/data/json/"
	@echo "Running multi-sensor tool for 3 seconds..."
	@timeout 3s $(MULTI_SENSOR_TARGET) -f $(TEST_DIR)/configs/test_config.txt -g json-only-test -d 2>/dev/null || true
	@echo "Quick test completed."

# 测试原始工具
test-original: original
	@echo "Testing original RTSP tool..."
	@if [ -f "$(TEST_DIR)/configs/rtspconfig.txt" ]; then \
		echo "Config file found, running test..."; \
		timeout 3s $(ORIGINAL_TARGET) -f $(TEST_DIR)/configs/rtspconfig.txt -g deqing-001 -d 2>/dev/null || true; \
	else \
		echo "No original config file found, skipping test"; \
	fi

# 显示帮助
help:
	@echo "RTSP Tools Project Makefile"
	@echo ""
	@echo "Available targets:"
	@echo "  all              - Build all tools (original + multi-sensor)"
	@echo "  original         - Build original RTSP tools"
	@echo "  multi-sensor     - Build multi-sensor tool"
	@echo "  clean            - Remove built files"
	@echo "  check-deps       - Check for required dependencies"
	@echo "  test             - Run multi-sensor test suite"
	@echo "  test-original    - Test original RTSP tool"
	@echo "  quick-test       - Run quick JSON sensor test"
	@echo "  test-data        - Generate test data files"
	@echo "  help             - Show this help message"
	@echo ""
	@echo "Dependency installation:"
	@echo "  install-deps-ubuntu  - Install deps on Ubuntu/Debian"
	@echo "  install-deps-centos  - Install deps on CentOS/RHEL"
	@echo "  install-deps-macos   - Install deps on macOS"
	@echo ""
	@echo "Usage examples:"
	@echo "  make all             # Build all tools"
	@echo "  make original        # Build original RTSP tool"
	@echo "  make multi-sensor    # Build multi-sensor tool"
	@echo "  make test            # Build and test multi-sensor"
	@echo "  make quick-test      # Quick functionality test"
	@echo "  make clean           # Clean up"

# 显示版本信息
version:
	@echo "RTSP Tools Project"
	@echo "Version: 2.0 (Structured Architecture)"
	@echo "Build system: Makefile"
	@echo ""
	@echo "Tools:"
	@if [ -f "$(ORIGINAL_TARGET)" ]; then \
		echo "  Original RTSP Tool: $(ORIGINAL_TARGET) (built)"; \
	else \
		echo "  Original RTSP Tool: Not built"; \
	fi
	@if [ -f "$(ORIGINAL_TIMESTAMP_TARGET)" ]; then \
		echo "  Timestamp Tool: $(ORIGINAL_TIMESTAMP_TARGET) (built)"; \
	else \
		echo "  Timestamp Tool: Not built"; \
	fi
	@if [ -f "$(MULTI_SENSOR_TARGET)" ]; then \
		echo "  Multi-Sensor Tool: $(MULTI_SENSOR_TARGET) (built)"; \
	else \
		echo "  Multi-Sensor Tool: Not built"; \
	fi

# 伪目标
.PHONY: all original multi-sensor clean check-deps install-deps-ubuntu install-deps-centos install-deps-macos test test-original quick-test test-data help version
