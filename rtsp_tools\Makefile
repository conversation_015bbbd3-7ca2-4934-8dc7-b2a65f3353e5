# Makefile for Multi-Sensor Data Synchronous Playback Tool
# 多传感器数据同步播放工具编译文件

# 编译器和标准
CXX = g++
CXXFLAGS = -std=c++11 -O2 -g -Wall -Wextra

# 目标文件
TARGET = multi_sensor_tool
SOURCES = multi_sensor_main.cpp multi_sensor_server.cpp
HEADERS = multi_sensor_server.h

# 依赖库检测
FFMPEG_CFLAGS = $(shell pkg-config --cflags libavformat libavcodec libavutil 2>/dev/null)
FFMPEG_LIBS = $(shell pkg-config --libs libavformat libavcodec libavutil 2>/dev/null)

# JsonCpp库检测
JSONCPP_CFLAGS = $(shell pkg-config --cflags jsoncpp 2>/dev/null || echo "-I/usr/include -I/usr/local/include")
JSONCPP_LIBS = $(shell pkg-config --libs jsoncpp 2>/dev/null || echo "-ljsoncpp")

# 平台特定的网络库
UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
    NETWORK_LIBS = 
endif
ifeq ($(UNAME_S),Darwin)
    NETWORK_LIBS = 
endif
ifneq (,$(findstring MINGW,$(UNAME_S)))
    NETWORK_LIBS = -lws2_32
endif
ifneq (,$(findstring CYGWIN,$(UNAME_S)))
    NETWORK_LIBS = -lws2_32
endif

# 所有编译标志
ALL_CFLAGS = $(CXXFLAGS) $(FFMPEG_CFLAGS) $(JSONCPP_CFLAGS)
ALL_LIBS = $(FFMPEG_LIBS) $(JSONCPP_LIBS) $(NETWORK_LIBS) -lpthread

# 默认目标
all: check-deps $(TARGET)

# 检查依赖
check-deps:
	@echo "Checking dependencies..."
	@if ! pkg-config --exists libavformat libavcodec libavutil; then \
		echo "Error: FFmpeg development libraries not found."; \
		echo "Please install FFmpeg development packages:"; \
		echo "  Ubuntu/Debian: sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev"; \
		echo "  CentOS/RHEL: sudo yum install ffmpeg-devel"; \
		echo "  macOS: brew install ffmpeg"; \
		exit 1; \
	fi
	@if ! pkg-config --exists jsoncpp && ! [ -f /usr/include/json/json.h ] && ! [ -f /usr/local/include/json/json.h ]; then \
		echo "Warning: JsonCpp not found. Please install:"; \
		echo "  Ubuntu/Debian: sudo apt-get install libjsoncpp-dev"; \
		echo "  CentOS/RHEL: sudo yum install jsoncpp-devel"; \
		echo "  macOS: brew install jsoncpp"; \
	fi
	@echo "Dependencies check completed."

# 编译目标
$(TARGET): $(SOURCES) $(HEADERS)
	@echo "Compiling $(TARGET)..."
	@echo "CFLAGS: $(ALL_CFLAGS)"
	@echo "LIBS: $(ALL_LIBS)"
	$(CXX) $(ALL_CFLAGS) -o $(TARGET) $(SOURCES) $(ALL_LIBS)
	@echo "Build successful: $(TARGET)"
	@ls -lh $(TARGET)

# 清理
clean:
	@echo "Cleaning up..."
	rm -f $(TARGET)
	rm -f test_radar.json test_lidar.json
	rm -f *.o *.core
	@echo "Clean completed."

# 安装依赖（Ubuntu/Debian）
install-deps-ubuntu:
	@echo "Installing dependencies on Ubuntu/Debian..."
	sudo apt-get update
	sudo apt-get install -y libavformat-dev libavcodec-dev libavutil-dev libjsoncpp-dev pkg-config build-essential

# 安装依赖（CentOS/RHEL）
install-deps-centos:
	@echo "Installing dependencies on CentOS/RHEL..."
	sudo yum install -y ffmpeg-devel jsoncpp-devel pkgconfig gcc-c++

# 安装依赖（macOS）
install-deps-macos:
	@echo "Installing dependencies on macOS..."
	brew install ffmpeg jsoncpp pkg-config

# 生成测试数据
test-data:
	@echo "Generating test data..."
	@./test_multi_sensor.sh

# 运行测试
test: $(TARGET) test-data
	@echo "Running tests..."
	chmod +x test_multi_sensor.sh
	./test_multi_sensor.sh

# 快速测试（仅JSON传感器）
quick-test: $(TARGET)
	@echo "Running quick test..."
	@echo "Generating minimal test data..."
	@echo '[{"timestamp": 1723169047000, "data": {"test": "radar"}}]' > test_radar.json
	@echo '[{"timestamp": 1723169047000, "data": {"test": "lidar"}}]' > test_lidar.json
	@echo "Starting UDP listeners..."
	@(nc -u -l 9001 &) 2>/dev/null || true
	@(nc -u -l 9002 &) 2>/dev/null || true
	@sleep 1
	@echo "Running tool for 3 seconds..."
	@timeout 3s ./$(TARGET) -f test_config.txt -g json-only-test -d || true
	@echo "Quick test completed."

# 显示帮助
help:
	@echo "Multi-Sensor Tool Makefile"
	@echo ""
	@echo "Available targets:"
	@echo "  all              - Build the multi-sensor tool (default)"
	@echo "  clean            - Remove built files"
	@echo "  check-deps       - Check for required dependencies"
	@echo "  test             - Run full test suite"
	@echo "  quick-test       - Run quick JSON sensor test"
	@echo "  test-data        - Generate test data files"
	@echo "  help             - Show this help message"
	@echo ""
	@echo "Dependency installation:"
	@echo "  install-deps-ubuntu  - Install deps on Ubuntu/Debian"
	@echo "  install-deps-centos  - Install deps on CentOS/RHEL"
	@echo "  install-deps-macos   - Install deps on macOS"
	@echo ""
	@echo "Usage examples:"
	@echo "  make                 # Build the tool"
	@echo "  make test            # Build and test"
	@echo "  make quick-test      # Quick functionality test"
	@echo "  make clean           # Clean up"

# 显示版本信息
version:
	@echo "Multi-Sensor Data Synchronous Playback Tool"
	@echo "Version: 1.0 (Simple Architecture)"
	@echo "Build system: Makefile"
	@if [ -f $(TARGET) ]; then \
		echo "Executable: $(TARGET) ($(shell ls -lh $(TARGET) | awk '{print $$5}'))"; \
	else \
		echo "Executable: Not built"; \
	fi

# 伪目标
.PHONY: all clean check-deps install-deps-ubuntu install-deps-centos install-deps-macos test quick-test test-data help version
