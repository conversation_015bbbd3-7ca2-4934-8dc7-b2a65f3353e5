#include "multi_sensor_server.h"
#include <json/json.h>  // 需要安装jsoncpp库
#include <iomanip>

// SimpleUDPSender 实现
SimpleUDPSender::SimpleUDPSender(const std::string& address, int port)
    : target_address_(address), target_port_(port) {
#ifdef _WIN32
    WSADATA wsaData;
    WSAStartup(MAKEWORD(2, 2), &wsaData);
#endif
}

SimpleUDPSender::~SimpleUDPSender() {
    cleanup();
#ifdef _WIN32
    WSACleanup();
#endif
}

bool SimpleUDPSender::initialize() {
    if (initialized_) return true;
    
    if (!createSocket()) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR, 
            "Failed to create UDP socket for " + target_address_ + ":" + std::to_string(target_port_));
        return false;
    }
    
    if (!setupAddress()) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR, 
            "Failed to setup UDP address for " + target_address_ + ":" + std::to_string(target_port_));
        return false;
    }
    
    initialized_ = true;
    SimpleErrorHandler::logError(SimpleErrorHandler::INFO, 
        "UDP sender initialized for " + target_address_ + ":" + std::to_string(target_port_));
    return true;
}

bool SimpleUDPSender::createSocket() {
    socket_fd_ = socket(AF_INET, SOCK_DGRAM, 0);
#ifdef _WIN32
    return socket_fd_ != INVALID_SOCKET;
#else
    return socket_fd_ >= 0;
#endif
}

bool SimpleUDPSender::setupAddress() {
    memset(&server_addr_, 0, sizeof(server_addr_));
    server_addr_.sin_family = AF_INET;
    server_addr_.sin_port = htons(target_port_);
    
    if (inet_pton(AF_INET, target_address_.c_str(), &server_addr_.sin_addr) <= 0) {
        return false;
    }
    
    return true;
}

bool SimpleUDPSender::sendData(const std::string& data) {
    if (!initialized_) {
        if (!initialize()) {
            return false;
        }
    }
    
    ssize_t sent = sendto(socket_fd_, data.c_str(), data.length(), 0,
                         (struct sockaddr*)&server_addr_, sizeof(server_addr_));
    
    if (sent < 0) {
        SimpleErrorHandler::logError(SimpleErrorHandler::WARNING, 
            "Failed to send UDP data to " + target_address_ + ":" + std::to_string(target_port_));
        return false;
    }
    
    return true;
}

void SimpleUDPSender::cleanup() {
    if (socket_fd_ >= 0) {
#ifdef _WIN32
        closesocket(socket_fd_);
#else
        close(socket_fd_);
#endif
        socket_fd_ = -1;
    }
    initialized_ = false;
}

// JsonSensor 实现
JsonSensor::JsonSensor(const std::string& json_file, const std::string& udp_address, 
                       int udp_port, SensorType type)
    : json_file_(json_file), udp_address_(udp_address), udp_port_(udp_port), sensor_type_(type) {
    udp_sender_ = std::make_unique<SimpleUDPSender>(udp_address, udp_port);
}

bool JsonSensor::initialize() {
    if (initialized_) return true;
    
    // 加载JSON数据
    if (!loadJsonData()) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR, 
            "Failed to load JSON data from " + json_file_);
        return false;
    }
    
    // 初始化UDP发送器
    if (!udp_sender_->initialize()) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR, 
            "Failed to initialize UDP sender for " + getOutputAddress());
        return false;
    }
    
    initialized_ = true;
    SimpleErrorHandler::logError(SimpleErrorHandler::INFO, 
        "JsonSensor initialized: " + json_file_ + " -> " + getOutputAddress() + 
        " (" + std::to_string(frames_.size()) + " frames)");
    return true;
}

bool JsonSensor::loadJsonData() {
    try {
        parseJsonFile(json_file_);
        
        if (frames_.empty()) {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR, 
                "No valid frames found in " + json_file_);
            return false;
        }
        
        // 按时间戳排序
        std::sort(frames_.begin(), frames_.end(), 
                 [](const JsonFrame& a, const JsonFrame& b) {
                     return a.timestamp < b.timestamp;
                 });
        
        SimpleErrorHandler::logError(SimpleErrorHandler::INFO, 
            "Loaded " + std::to_string(frames_.size()) + " frames from " + json_file_);
        return true;
        
    } catch (const std::exception& e) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR, 
            "Exception loading JSON data: " + std::string(e.what()));
        return false;
    }
}

void JsonSensor::parseJsonFile(const std::string& file_path) {
    std::ifstream file(file_path);
    if (!file.is_open()) {
        throw std::runtime_error("Cannot open file: " + file_path);
    }
    
    // 检查文件大小
    file.seekg(0, std::ios::end);
    size_t file_size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    const size_t MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
    if (file_size > MAX_FILE_SIZE) {
        throw std::runtime_error("File too large: " + file_path + 
                                " (" + std::to_string(file_size) + " bytes)");
    }
    
    Json::Value root;
    Json::Reader reader;
    
    if (!reader.parse(file, root)) {
        throw std::runtime_error("Failed to parse JSON: " + reader.getFormattedErrorMessages());
    }
    
    if (!root.isArray()) {
        throw std::runtime_error("JSON root must be an array");
    }
    
    for (const auto& item : root) {
        if (!item.isMember("timestamp") || !item.isMember("data")) {
            SimpleErrorHandler::logError(SimpleErrorHandler::WARNING, 
                "Skipping invalid JSON frame (missing timestamp or data)");
            continue;
        }
        
        int64_t timestamp = item["timestamp"].asInt64();
        std::string data = item["data"].toStyledString();
        
        frames_.emplace_back(timestamp, data);
    }
}

bool JsonSensor::processFrame(int64_t current_time) {
    if (!initialized_ || !hasMoreData()) {
        return false;
    }
    
    const JsonFrame& frame = frames_[current_index_];
    std::string output_message = createOutputMessage(frame);
    
    bool success = udp_sender_->sendData(output_message);
    if (success) {
        current_index_++;
        if (current_index_ < frames_.size()) {
            SimpleErrorHandler::logError(SimpleErrorHandler::INFO, 
                "Sent frame " + std::to_string(current_index_) + "/" + 
                std::to_string(frames_.size()) + " to " + getOutputAddress());
        }
    }
    
    return success;
}

int64_t JsonSensor::getCurrentTimestamp() {
    if (!hasMoreData()) {
        return INT64_MAX;
    }
    return frames_[current_index_].timestamp;
}

bool JsonSensor::hasMoreData() {
    return current_index_ < frames_.size();
}

void JsonSensor::reset() {
    current_index_ = 0;
    SimpleErrorHandler::logError(SimpleErrorHandler::INFO, 
        "Reset JsonSensor: " + json_file_);
}

std::string JsonSensor::createOutputMessage(const JsonFrame& frame) {
    Json::Value output;
    output["timestamp"] = frame.timestamp;
    output["sensor_type"] = (sensor_type_ == RADAR) ? "radar" : "lidar";
    
    Json::Reader reader;
    Json::Value data;
    if (reader.parse(frame.data, data)) {
        output["data"] = data;
    } else {
        output["data"] = frame.data;
    }
    
    Json::StreamWriterBuilder builder;
    builder["indentation"] = "";
    return Json::writeString(builder, output);
}

// SimpleConfigParser 实现
std::vector<SimpleConfigParser::SensorConfig> SimpleConfigParser::parseGroup(
    const std::string& file_path, const std::string& group_name) {
    
    std::vector<SensorConfig> configs;
    std::ifstream file(file_path);
    
    if (!file.is_open()) {
        SimpleErrorHandler::logError(SimpleErrorHandler::FATAL, 
            "Cannot open config file: " + file_path);
        return configs;
    }
    
    std::string line;
    bool in_target_group = false;
    std::string current_group;
    
    while (std::getline(file, line)) {
        line = trim(line);
        
        // 跳过空行和注释
        if (line.empty() || line[0] == '#') {
            continue;
        }
        
        // 检查是否是组标识
        if (line[0] == '[' && line.back() == ']') {
            current_group = line.substr(1, line.length() - 2);
            in_target_group = (current_group == group_name);
            continue;
        }
        
        // 如果在目标组中，解析配置行
        if (in_target_group) {
            SensorConfig config;
            
            if (isLegacyFormat(line)) {
                config = parseLegacyLine(line);
            } else {
                config = parseNewFormatLine(line);
            }
            
            if (!config.type.empty()) {
                configs.push_back(config);
            }
        }
    }
    
    if (configs.empty()) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR, 
            "No valid sensor configurations found in group: " + group_name);
    } else {
        SimpleErrorHandler::logError(SimpleErrorHandler::INFO, 
            "Loaded " + std::to_string(configs.size()) + 
            " sensor configurations from group: " + group_name);
    }
    
    return configs;
}

bool SimpleConfigParser::isLegacyFormat(const std::string& line) {
    // 旧格式没有类型前缀，直接以文件名开始
    return line.find(':') == std::string::npos || 
           line.find(':') > line.find(',');
}

SimpleConfigParser::SensorConfig SimpleConfigParser::parseLegacyLine(const std::string& line) {
    SensorConfig config;
    auto parts = split(line, ',');
    
    if (parts.size() >= 3) {
        config.type = "video";  // 旧格式默认为视频
        config.data_file = trim(parts[0]);
        config.timestamp_file = trim(parts[1]);
        config.output_address = trim(parts[2]);
    }
    
    return config;
}

SimpleConfigParser::SensorConfig SimpleConfigParser::parseNewFormatLine(const std::string& line) {
    SensorConfig config;
    
    size_t colon_pos = line.find(':');
    if (colon_pos == std::string::npos) {
        return config;
    }
    
    config.type = trim(line.substr(0, colon_pos));
    std::string rest = line.substr(colon_pos + 1);
    auto parts = split(rest, ',');
    
    if (config.type == "video" || config.type == "fisheye") {
        if (parts.size() >= 3) {
            config.data_file = trim(parts[0]);
            config.timestamp_file = trim(parts[1]);
            config.output_address = trim(parts[2]);
        }
    } else if (config.type == "radar" || config.type == "lidar") {
        if (parts.size() >= 2) {
            config.data_file = trim(parts[0]);
            std::string udp_info = trim(parts[1]);
            
            // 解析UDP地址和端口
            size_t last_colon = udp_info.find_last_of(':');
            if (last_colon != std::string::npos) {
                config.output_address = udp_info.substr(0, last_colon);
                config.output_port = std::stoi(udp_info.substr(last_colon + 1));
            }
        }
    }
    
    return config;
}

std::string SimpleConfigParser::trim(const std::string& str) {
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) return "";
    
    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

std::vector<std::string> SimpleConfigParser::split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::stringstream ss(str);
    std::string token;
    
    while (std::getline(ss, token, delimiter)) {
        tokens.push_back(token);
    }
    
    return tokens;
}

// VideoSensor 实现
VideoSensor::VideoSensor(const std::string& ts_file, const std::string& txt_file,
                        const std::string& rtsp_url, SensorType type,
                        bool is_loop, bool is_debug, uint16_t camera_type,
                        int64_t earliest_timestamp)
    : stream_context_(ts_file, txt_file, rtsp_url), sensor_type_(type),
      is_loop_(is_loop), is_debug_(is_debug), camera_type_(camera_type),
      earliest_timestamp_(earliest_timestamp) {
}

bool VideoSensor::initialize() {
    if (initialized_) return true;

    try {
        // 读取时间戳文件
        stream_context_.time_base = readFileToInt64(stream_context_.input_file_txt);
        if (stream_context_.time_base.empty()) {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
                "Cannot read timestamp file: " + stream_context_.input_file_txt);
            return false;
        }

        // 初始化视频流
        if (init_stream() < 0) {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
                "Failed to initialize video stream: " + stream_context_.input_file_ts);
            return false;
        }

        initialized_ = true;
        SimpleErrorHandler::logError(SimpleErrorHandler::INFO,
            "VideoSensor initialized: " + stream_context_.input_file_ts + " -> " +
            stream_context_.output_url + " (" + std::to_string(stream_context_.time_base.size()) + " frames)");
        return true;

    } catch (const std::exception& e) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
            "Exception initializing VideoSensor: " + std::string(e.what()));
        return false;
    }
}

bool VideoSensor::processFrame(int64_t current_time) {
    if (!initialized_ || !hasMoreData()) {
        return false;
    }

    int64_t time_interval = current_time - earliest_timestamp_;

    if (!shouldProcessFrame(current_time, time_interval)) {
        return true; // 还没到播放时间，但不是错误
    }

    // 读取一帧数据
    int ret = av_read_frame(stream_context_.input_ctx, &stream_context_.pkt);

    if (ret == AVERROR_EOF) {
        // 文件结束
        return false;
    } else if (ret < 0) {
        // 读取错误，重试
        stream_context_.retry_count++;
        if (stream_context_.retry_count >= 3) {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
                "Max retries reached for " + stream_context_.input_file_ts);
            return false;
        }
        return true;
    } else {
        // 成功读取帧
        handleVideoFrame();
        stream_context_.retry_count = 0;
        stream_context_.frame_seq++;
        return true;
    }
}

bool VideoSensor::shouldProcessFrame(int64_t current_time, int64_t time_interval) {
    if (stream_context_.frame_seq >= stream_context_.time_base.size()) {
        return false;
    }

    // 复用原有的时间判断逻辑
    return (time_interval + stream_context_.time_base[stream_context_.frame_seq]) <= current_time;
}

void VideoSensor::handleVideoFrame() {
    // 如果是视频帧，处理SEI帧
    if (stream_context_.input_ctx->streams[stream_context_.pkt.stream_index]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
        process_packet(&stream_context_.pkt);
    }

    // 处理时间戳
    handle_timestamps();

    // 写入输出流（推送到RTSP）
    if (av_interleaved_write_frame(stream_context_.output_ctx, &stream_context_.pkt) < 0) {
        SimpleErrorHandler::logError(SimpleErrorHandler::WARNING,
            "Failed to write frame: " + stream_context_.input_file_ts);
    }

    // 释放数据包内存
    av_packet_unref(&stream_context_.pkt);
}

int64_t VideoSensor::getCurrentTimestamp() {
    if (!hasMoreData()) {
        return INT64_MAX;
    }
    return stream_context_.time_base[stream_context_.frame_seq];
}

bool VideoSensor::hasMoreData() {
    return stream_context_.frame_seq < stream_context_.time_base.size();
}

void VideoSensor::reset() {
    // 重置到文件开头
    av_seek_frame(stream_context_.input_ctx, -1, 0, AVSEEK_FLAG_BACKWARD);
    stream_context_.frame_seq = 0;
    SimpleErrorHandler::logError(SimpleErrorHandler::INFO,
        "Reset VideoSensor: " + stream_context_.input_file_ts);
}

// 以下是复用原有代码的辅助函数（简化版本）
std::vector<int64_t> VideoSensor::readFileToInt64(const std::string& filePath) {
    std::vector<int64_t> result;
    std::ifstream file(filePath);

    if (!file.is_open()) {
        throw std::runtime_error("Cannot open timestamp file: " + filePath);
    }

    std::string line;
    while (std::getline(file, line)) {
        // 移除Windows/Linux换行符差异
        if (!line.empty() && (line.back() == '\r' || line.back() == '\n')) {
            line.pop_back();
        }
        if (!line.empty() && (line.back() == '\r' || line.back() == '\n')) {
            line.pop_back();
        }

        // 跳过空行和注释
        if (line.empty() || line[0] == '#') {
            continue;
        }

        try {
            int64_t timestamp = std::stoll(line);
            result.push_back(timestamp);
        } catch (const std::exception& e) {
            SimpleErrorHandler::logError(SimpleErrorHandler::WARNING,
                "Invalid timestamp in " + filePath + ": " + line);
        }
    }

    return result;
}

int VideoSensor::init_stream() {
    // 复用原有的init_stream逻辑（简化版）
    // 1. 打开输入文件
    if (avformat_open_input(&stream_context_.input_ctx, stream_context_.input_file_ts.c_str(), nullptr, nullptr) < 0) {
        return -1;
    }

    // 2. 获取流信息
    if (avformat_find_stream_info(stream_context_.input_ctx, nullptr) < 0) {
        avformat_close_input(&stream_context_.input_ctx);
        return -1;
    }

    // 3. 创建输出上下文
    if (avformat_alloc_output_context2(&stream_context_.output_ctx, nullptr, "rtsp", stream_context_.output_url.c_str()) < 0) {
        avformat_close_input(&stream_context_.input_ctx);
        return -1;
    }

    // 4. 复制流参数
    for (unsigned i = 0; i < stream_context_.input_ctx->nb_streams; i++) {
        AVStream *out_stream = avformat_new_stream(stream_context_.output_ctx, nullptr);
        AVStream *in_stream = stream_context_.input_ctx->streams[i];

        avcodec_parameters_copy(out_stream->codecpar, in_stream->codecpar);
        out_stream->time_base = in_stream->time_base;

        if (in_stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
            AVRational avg_frame_rate = in_stream->avg_frame_rate;
            double fps = av_q2d(avg_frame_rate);
            stream_context_.average_interval = (1000 / fps) * 90;
        }
    }

    // 5. 建立网络连接
    if (!(stream_context_.output_ctx->oformat->flags & AVFMT_NOFILE)) {
        if (avio_open(&stream_context_.output_ctx->pb, stream_context_.output_url.c_str(), AVIO_FLAG_WRITE) < 0) {
            avformat_free_context(stream_context_.output_ctx);
            avformat_close_input(&stream_context_.input_ctx);
            return -1;
        }
    }

    // 6. 写入流头部
    if (avformat_write_header(stream_context_.output_ctx, nullptr) < 0) {
        if (!(stream_context_.output_ctx->oformat->flags & AVFMT_NOFILE)) {
            avio_closep(&stream_context_.output_ctx->pb);
        }
        avformat_free_context(stream_context_.output_ctx);
        avformat_close_input(&stream_context_.input_ctx);
        return -1;
    }

    return 0;
}

void VideoSensor::handle_timestamps() {
    // 复用原有的时间戳处理逻辑（简化版）
    AVStream *in_stream = stream_context_.input_ctx->streams[stream_context_.pkt.stream_index];
    AVStream *out_stream = stream_context_.output_ctx->streams[stream_context_.pkt.stream_index];

    if (is_loop_) {
        if (stream_context_.last_dts[stream_context_.pkt.stream_index] == 0) {
            stream_context_.last_dts[stream_context_.pkt.stream_index] = stream_context_.pkt.dts;
        } else {
            stream_context_.pkt.dts = stream_context_.last_dts[stream_context_.pkt.stream_index] + stream_context_.average_interval;
        }
    }

    stream_context_.pkt.pts = stream_context_.pkt.dts;

    if (stream_context_.pkt.dts <= stream_context_.last_dts[stream_context_.pkt.stream_index]) {
        stream_context_.pkt.dts = stream_context_.last_dts[stream_context_.pkt.stream_index] + 1;
        stream_context_.pkt.pts = stream_context_.pkt.dts;
    }

    stream_context_.last_dts[stream_context_.pkt.stream_index] = stream_context_.pkt.dts;
    av_packet_rescale_ts(&stream_context_.pkt, in_stream->time_base, out_stream->time_base);
}

void VideoSensor::process_packet(AVPacket *pkt) {
    // 简化的SEI帧处理，如果需要可以复用原有的复杂逻辑
    if (camera_type_ > 0) {
        // 这里可以添加SEI帧处理逻辑
        // 为了简化，暂时跳过
    }
}

uint8_t* VideoSensor::create_sei_frame(size_t *out_size) {
    // 简化的SEI帧创建，如果需要可以复用原有逻辑
    *out_size = 0;
    return nullptr;
}

int64_t VideoSensor::get_timestamp_ms() {
    auto now = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch());
    return duration.count();
}

// SimpleMultiSensorManager 实现
SimpleMultiSensorManager::SimpleMultiSensorManager(bool is_loop, bool is_debug, uint16_t camera_type)
    : is_loop_(is_loop), is_debug_(is_debug), camera_type_(camera_type) {
}

bool SimpleMultiSensorManager::loadConfiguration(const std::string& config_file, const std::string& group_name) {
    SimpleConfigParser parser;
    auto configs = parser.parseGroup(config_file, group_name);

    if (configs.empty()) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
            "No sensor configurations found in group: " + group_name);
        return false;
    }

    // 创建传感器实例
    for (const auto& config : configs) {
        auto sensor = createSensor(config);
        if (sensor) {
            sensors_.push_back(std::move(sensor));
        }
    }

    if (sensors_.empty()) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
            "Failed to create any sensors");
        return false;
    }

    // 查找最早时间戳
    findEarliestTimestamp();

    SimpleErrorHandler::logError(SimpleErrorHandler::INFO,
        "Loaded " + std::to_string(sensors_.size()) + " sensors, earliest timestamp: " +
        std::to_string(earliest_timestamp_));

    return true;
}

std::unique_ptr<SensorBase> SimpleMultiSensorManager::createSensor(const SimpleConfigParser::SensorConfig& config) {
    try {
        if (config.type == "video") {
            return std::make_unique<VideoSensor>(
                config.data_file, config.timestamp_file, config.output_address,
                SensorBase::VIDEO, is_loop_, is_debug_, camera_type_, earliest_timestamp_);
        } else if (config.type == "fisheye") {
            return std::make_unique<VideoSensor>(
                config.data_file, config.timestamp_file, config.output_address,
                SensorBase::FISHEYE_VIDEO, is_loop_, is_debug_, camera_type_, earliest_timestamp_);
        } else if (config.type == "radar") {
            return std::make_unique<JsonSensor>(
                config.data_file, config.output_address, config.output_port,
                SensorBase::RADAR);
        } else if (config.type == "lidar") {
            return std::make_unique<JsonSensor>(
                config.data_file, config.output_address, config.output_port,
                SensorBase::LIDAR);
        } else {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
                "Unknown sensor type: " + config.type);
            return nullptr;
        }
    } catch (const std::exception& e) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
            "Failed to create sensor: " + std::string(e.what()));
        return nullptr;
    }
}

void SimpleMultiSensorManager::findEarliestTimestamp() {
    earliest_timestamp_ = INT64_MAX;

    for (auto& sensor : sensors_) {
        // 临时初始化传感器以获取时间戳信息
        if (sensor->initialize()) {
            int64_t sensor_timestamp = sensor->getCurrentTimestamp();
            if (sensor_timestamp < earliest_timestamp_) {
                earliest_timestamp_ = sensor_timestamp;
            }
        }
    }

    if (earliest_timestamp_ == INT64_MAX) {
        earliest_timestamp_ = get_timestamp_ms(); // 使用当前时间作为默认值
    }
}

bool SimpleMultiSensorManager::initializeAllSensors() {
    bool all_success = true;

    for (auto& sensor : sensors_) {
        if (!sensor->initialize()) {
            SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
                "Failed to initialize sensor: " + sensor->getDataFile());
            all_success = false;
        }
    }

    if (!all_success) {
        SimpleErrorHandler::logError(SimpleErrorHandler::WARNING,
            "Some sensors failed to initialize");
    }

    return all_success;
}

void SimpleMultiSensorManager::startPlayback() {
    if (sensors_.empty()) {
        SimpleErrorHandler::logError(SimpleErrorHandler::ERROR,
            "No sensors available for playback");
        return;
    }

    // 设置FFmpeg日志级别
    if (is_debug_) {
        av_log_set_level(AV_LOG_DEBUG);
    } else {
        av_log_set_level(AV_LOG_FATAL);
    }

    // 初始化FFmpeg网络组件
    avformat_network_init();

    running_ = true;
    SimpleErrorHandler::logError(SimpleErrorHandler::INFO,
        "Starting playback with " + std::to_string(sensors_.size()) + " sensors");

    playbackLoop();
}

void SimpleMultiSensorManager::stopPlayback() {
    running_ = false;
    SimpleErrorHandler::logError(SimpleErrorHandler::INFO, "Stopping playback");
}

void SimpleMultiSensorManager::playbackLoop() {
    // 复用原有的播放循环逻辑
    auto time_now = get_timestamp_ms();
    uint64_t time_interval = time_now - earliest_timestamp_;

    while (running_) {
        time_now = get_timestamp_ms();

        bool any_sensor_has_data = false;

        // 遍历所有传感器
        for (auto& sensor : sensors_) {
            if (!sensor->hasMoreData()) {
                continue;
            }

            any_sensor_has_data = true;

            // 检查是否应该处理这个传感器
            if (shouldProcessSensor(sensor.get(), time_now, time_interval)) {
                if (!sensor->processFrame(time_now)) {
                    if (is_debug_) {
                        SimpleErrorHandler::logError(SimpleErrorHandler::WARNING,
                            "Failed to process frame for sensor: " + sensor->getDataFile());
                    }
                }
            }
        }

        // 检查是否所有传感器都播放完毕
        if (!any_sensor_has_data) {
            if (!is_loop_) {
                SimpleErrorHandler::logError(SimpleErrorHandler::INFO,
                    "All sensors finished, exiting");
                break;
            }

            // 循环播放：重置所有传感器
            SimpleErrorHandler::logError(SimpleErrorHandler::INFO,
                "All sensors finished, restarting loop");
            resetAllSensors();
            time_interval = time_now - earliest_timestamp_;
        }

        // 保持原有的休眠间隔
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }

    SimpleErrorHandler::logError(SimpleErrorHandler::INFO, "Playback loop ended");
}

bool SimpleMultiSensorManager::shouldProcessSensor(SensorBase* sensor, int64_t current_time, int64_t time_interval) {
    // 复用原有的时间判断逻辑
    int64_t sensor_timestamp = sensor->getCurrentTimestamp();
    if (sensor_timestamp == INT64_MAX) {
        return false; // 传感器没有更多数据
    }

    return (time_interval + sensor_timestamp) <= current_time;
}

void SimpleMultiSensorManager::resetAllSensors() {
    for (auto& sensor : sensors_) {
        sensor->reset();
    }
}

int64_t SimpleMultiSensorManager::get_timestamp_ms() {
    auto now = std::chrono::system_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch());
    return duration.count();
}
