# 多传感器数据同步播放工具 - 实现版本

基于简化架构设计的多传感器数据同步播放工具实现。

## 功能特性

- ✅ **向后兼容**: 完全兼容原有RTSP工具功能
- ✅ **多传感器支持**: 视频、鱼眼、Radar、Lidar数据同步播放
- ✅ **时序精确**: 保持原有2ms精度的时序控制
- ✅ **简化架构**: 最小化改动，快速实现
- ✅ **配置扩展**: 支持新格式同时兼容旧格式

## 支持的传感器类型

| 传感器类型 | 数据格式 | 输出方式 | 说明 |
|-----------|---------|---------|------|
| video | TS + 时间戳文件 | RTSP推流 | 普通视频流 |
| fisheye | TS + 时间戳文件 | RTSP推流 | 鱼眼视频流 |
| radar | JSON文件 | UDP数据包 | 雷达检测数据 |
| lidar | JSON文件 | UDP数据包 | 激光雷达点云数据 |

## 快速开始

### 1. 安装依赖

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev libjsoncpp-dev pkg-config build-essential
```

#### CentOS/RHEL
```bash
sudo yum install ffmpeg-devel jsoncpp-devel pkgconfig gcc-c++
```

#### macOS
```bash
brew install ffmpeg jsoncpp pkg-config
```

### 2. 编译工具

#### 方法1: 使用Makefile（推荐）
```bash
make                # 编译
make test          # 编译并测试
make quick-test    # 快速功能测试
```

#### 方法2: 使用编译脚本
```bash
chmod +x build_multi_sensor.sh
./build_multi_sensor.sh
```

#### 方法3: 手动编译
```bash
g++ -std=c++11 -O2 -g -Wall -Wextra \
    -o multi_sensor_tool \
    multi_sensor_main.cpp multi_sensor_server.cpp \
    $(pkg-config --cflags --libs libavformat libavcodec libavutil jsoncpp) \
    -lpthread
```

### 3. 运行测试

```bash
# 生成测试数据并运行完整测试
chmod +x test_multi_sensor.sh
./test_multi_sensor.sh

# 或者使用Makefile
make test
```

## 使用方法

### 命令行语法

```bash
./multi_sensor_tool -f <配置文件> -g <传感器组名称> [选项]

必需参数:
  -f <配置文件>    INI格式的配置文件
  -g <组名称>      要播放的传感器组名称

可选参数:
  -d              启用调试输出
  -l              启用循环播放模式
  -t <类型>       设置视频SEI帧类型 (0: 默认, 1: 大华, 2: 海康)
  -h              显示帮助信息
```

### 配置文件格式

#### 新格式（支持多传感器）
```ini
[scene-001]
# 格式: 传感器类型:数据文件,时间戳文件,输出地址
video:camera1.ts,camera1.txt,rtsp://192.168.1.100:8554/stream1
fisheye:fisheye1.ts,fisheye1.txt,rtsp://192.168.1.100:8554/stream2
radar:radar_data.json,udp://192.168.1.100:9001
lidar:lidar_data.json,udp://192.168.1.100:9002
```

#### 旧格式（向后兼容）
```ini
[legacy-group]
# 原有格式自动识别为video类型
video1.ts,video1.txt,rtsp://192.168.1.100:8554/stream1
video2.ts,video2.txt,rtsp://192.168.1.100:8554/stream2
```

### JSON数据格式

#### Radar数据示例
```json
[
  {
    "timestamp": 1723169047000,
    "data": {
      "sensor_id": "radar_001",
      "detection_count": 3,
      "detections": [
        {"range": 25.6, "angle": 15.2, "velocity": 12.3}
      ]
    }
  }
]
```

#### Lidar数据示例
```json
[
  {
    "timestamp": 1723169047000,
    "data": {
      "sensor_id": "lidar_001",
      "point_count": 1024,
      "points": [
        {"x": 1.5, "y": 2.3, "z": 0.1, "intensity": 120}
      ]
    }
  }
]
```

## 使用示例

### 1. 播放多传感器场景
```bash
./multi_sensor_tool -f config.txt -g scene-001 -l -d
```

### 2. 向后兼容播放
```bash
./multi_sensor_tool -f rtspconfig.txt -g deqing-001 -l
```

### 3. 仅JSON传感器测试
```bash
./multi_sensor_tool -f test_config.txt -g json-only-test -d
```

### 4. 监控UDP输出
```bash
# 在另一个终端监控雷达数据
nc -u -l 9001

# 在另一个终端监控激光雷达数据
nc -u -l 9002
```

## 测试和验证

### 1. 运行完整测试套件
```bash
make test
```

### 2. 快速功能测试
```bash
make quick-test
```

### 3. 手动测试步骤

1. **生成测试数据**:
   ```bash
   # 测试脚本会自动生成test_radar.json和test_lidar.json
   ./test_multi_sensor.sh
   ```

2. **启动UDP监听器**:
   ```bash
   # 终端1: 监听雷达数据
   nc -u -l 9001
   
   # 终端2: 监听激光雷达数据
   nc -u -l 9002
   ```

3. **运行工具**:
   ```bash
   # 终端3: 运行多传感器工具
   ./multi_sensor_tool -f test_config.txt -g json-only-test -d
   ```

4. **观察输出**: 在UDP监听器终端应该能看到JSON数据输出

## 故障排除

### 1. 编译错误

**错误**: `fatal error: json/json.h: No such file or directory`
**解决**: 安装JsonCpp开发库
```bash
# Ubuntu/Debian
sudo apt-get install libjsoncpp-dev

# CentOS/RHEL
sudo yum install jsoncpp-devel

# macOS
brew install jsoncpp
```

**错误**: `fatal error: libavformat/avformat.h: No such file or directory`
**解决**: 安装FFmpeg开发库
```bash
# Ubuntu/Debian
sudo apt-get install libavformat-dev libavcodec-dev libavutil-dev

# CentOS/RHEL
sudo yum install ffmpeg-devel

# macOS
brew install ffmpeg
```

### 2. 运行时错误

**错误**: `Cannot open config file`
**解决**: 检查配置文件路径和权限

**错误**: `No valid sensor configurations found`
**解决**: 检查配置文件格式和组名称

**错误**: `Failed to initialize UDP sender`
**解决**: 检查网络配置和端口占用

### 3. UDP数据接收问题

**问题**: UDP监听器收不到数据
**解决**:
1. 检查防火墙设置
2. 确认端口未被占用
3. 检查IP地址配置
4. 使用`netstat -un`查看UDP端口状态

## 性能特性

- **内存使用**: JSON文件 < 100MB，视频复用FFmpeg管理
- **时序精度**: 保持原有2ms轮询精度
- **CPU占用**: 增量 < 10%
- **网络延迟**: UDP < 10ms

## 文件结构

```
rtsp_tools/
├── multi_sensor_server.h          # 头文件
├── multi_sensor_server.cpp        # 主要实现
├── multi_sensor_main.cpp          # 主程序
├── Makefile                       # 编译文件
├── build_multi_sensor.sh          # 编译脚本
├── test_multi_sensor.sh           # 测试脚本
├── test_config.txt               # 测试配置
├── README_MULTI_SENSOR.md        # 本文档
└── ARCHITECTURE_SIMPLE.md        # 架构设计文档
```

## 与原工具的差异

| 特性 | 原工具 | 多传感器工具 |
|------|--------|-------------|
| 传感器类型 | 仅视频 | 视频+JSON传感器 |
| 配置格式 | CSV格式 | 扩展INI格式 |
| 输出方式 | 仅RTSP | RTSP+UDP |
| 向后兼容 | N/A | 100%兼容 |
| 时序精度 | 2ms | 2ms（保持不变） |

## 后续扩展

基础功能稳定后，可以考虑以下扩展：
- 性能优化（如需要）
- 更复杂的内存管理（如需要）
- 图形化监控界面（如需要）
- 更多传感器类型支持（如需要）

## 技术支持

如有问题，请检查：
1. 依赖库是否正确安装
2. 配置文件格式是否正确
3. 网络配置是否正确
4. 查看调试输出（使用-d参数）
