#!/bin/bash

# Multi-Sensor Tool Test Script
# 测试多传感器工具的各项功能

echo "=== Multi-Sensor Tool Test Script ==="

# 检查工具是否已编译
if [ ! -f "multi_sensor_tool" ]; then
    echo "multi_sensor_tool not found. Building first..."
    chmod +x build_multi_sensor.sh
    ./build_multi_sensor.sh
    
    if [ ! -f "multi_sensor_tool" ]; then
        echo "Error: Failed to build multi_sensor_tool"
        exit 1
    fi
fi

# 生成测试JSON数据
echo "Generating test JSON data..."

# 生成测试雷达数据
cat > test_radar.json << 'EOF'
[
  {
    "timestamp": 1723169047000,
    "data": {
      "sensor_id": "radar_001",
      "detection_count": 3,
      "detections": [
        {"range": 25.6, "angle": 15.2, "velocity": 12.3},
        {"range": 30.1, "angle": 45.8, "velocity": 8.7},
        {"range": 18.9, "angle": -12.5, "velocity": 15.2}
      ]
    }
  },
  {
    "timestamp": 1723169047040,
    "data": {
      "sensor_id": "radar_001",
      "detection_count": 2,
      "detections": [
        {"range": 26.1, "angle": 16.0, "velocity": 12.1},
        {"range": 29.8, "angle": 44.2, "velocity": 9.1}
      ]
    }
  },
  {
    "timestamp": 1723169047080,
    "data": {
      "sensor_id": "radar_001",
      "detection_count": 4,
      "detections": [
        {"range": 26.5, "angle": 16.8, "velocity": 11.9},
        {"range": 29.5, "angle": 42.6, "velocity": 9.5},
        {"range": 35.2, "angle": 60.1, "velocity": 5.8},
        {"range": 22.1, "angle": -8.3, "velocity": 18.7}
      ]
    }
  },
  {
    "timestamp": 1723169047120,
    "data": {
      "sensor_id": "radar_001",
      "detection_count": 1,
      "detections": [
        {"range": 27.0, "angle": 17.5, "velocity": 11.7}
      ]
    }
  },
  {
    "timestamp": 1723169047160,
    "data": {
      "sensor_id": "radar_001",
      "detection_count": 5,
      "detections": [
        {"range": 27.4, "angle": 18.2, "velocity": 11.5},
        {"range": 29.2, "angle": 41.0, "velocity": 9.9},
        {"range": 34.8, "angle": 58.5, "velocity": 6.2},
        {"range": 21.8, "angle": -6.1, "velocity": 19.1},
        {"range": 40.5, "angle": 75.3, "velocity": 3.4}
      ]
    }
  }
]
EOF

# 生成测试激光雷达数据
cat > test_lidar.json << 'EOF'
[
  {
    "timestamp": 1723169047000,
    "data": {
      "sensor_id": "lidar_001",
      "point_count": 1024,
      "scan_frequency": 10.0,
      "points": [
        {"x": 1.5, "y": 2.3, "z": 0.1, "intensity": 120},
        {"x": 2.1, "y": 1.8, "z": 0.2, "intensity": 95},
        {"x": 0.8, "y": 3.2, "z": 0.0, "intensity": 150}
      ]
    }
  },
  {
    "timestamp": 1723169047050,
    "data": {
      "sensor_id": "lidar_001",
      "point_count": 1056,
      "scan_frequency": 10.0,
      "points": [
        {"x": 1.6, "y": 2.4, "z": 0.1, "intensity": 118},
        {"x": 2.2, "y": 1.9, "z": 0.2, "intensity": 97},
        {"x": 0.9, "y": 3.3, "z": 0.0, "intensity": 148}
      ]
    }
  },
  {
    "timestamp": 1723169047100,
    "data": {
      "sensor_id": "lidar_001",
      "point_count": 1089,
      "scan_frequency": 10.0,
      "points": [
        {"x": 1.7, "y": 2.5, "z": 0.1, "intensity": 116},
        {"x": 2.3, "y": 2.0, "z": 0.2, "intensity": 99},
        {"x": 1.0, "y": 3.4, "z": 0.0, "intensity": 146}
      ]
    }
  },
  {
    "timestamp": 1723169047150,
    "data": {
      "sensor_id": "lidar_001",
      "point_count": 1012,
      "scan_frequency": 10.0,
      "points": [
        {"x": 1.8, "y": 2.6, "z": 0.1, "intensity": 114},
        {"x": 2.4, "y": 2.1, "z": 0.2, "intensity": 101},
        {"x": 1.1, "y": 3.5, "z": 0.0, "intensity": 144}
      ]
    }
  },
  {
    "timestamp": 1723169047200,
    "data": {
      "sensor_id": "lidar_001",
      "point_count": 1078,
      "scan_frequency": 10.0,
      "points": [
        {"x": 1.9, "y": 2.7, "z": 0.1, "intensity": 112},
        {"x": 2.5, "y": 2.2, "z": 0.2, "intensity": 103},
        {"x": 1.2, "y": 3.6, "z": 0.0, "intensity": 142}
      ]
    }
  }
]
EOF

echo "Generated test_radar.json and test_lidar.json"

# 启动UDP监听器（用于测试）
echo "Starting UDP listeners for testing..."

# 启动雷达数据监听器
(
    echo "Radar UDP listener started on port 9001"
    nc -u -l 9001 | while read line; do
        echo "[RADAR] $(date '+%H:%M:%S.%3N'): $line"
    done
) &
RADAR_PID=$!

# 启动激光雷达数据监听器
(
    echo "Lidar UDP listener started on port 9002"
    nc -u -l 9002 | while read line; do
        echo "[LIDAR] $(date '+%H:%M:%S.%3N'): $line"
    done
) &
LIDAR_PID=$!

# 等待监听器启动
sleep 1

# 清理函数
cleanup() {
    echo ""
    echo "Cleaning up..."
    if [ ! -z "$RADAR_PID" ]; then
        kill $RADAR_PID 2>/dev/null
    fi
    if [ ! -z "$LIDAR_PID" ]; then
        kill $LIDAR_PID 2>/dev/null
    fi
    echo "Test completed"
}

# 设置信号处理
trap cleanup EXIT INT TERM

echo ""
echo "=== Running Tests ==="

# 测试1: 显示帮助信息
echo "Test 1: Help message"
./multi_sensor_tool -h
echo ""

# 测试2: 配置文件解析测试
echo "Test 2: Configuration parsing test"
echo "Testing JSON-only configuration..."
timeout 10s ./multi_sensor_tool -f test_config.txt -g json-only-test -d &
TEST_PID=$!

# 等待一段时间让工具运行
sleep 5

# 停止测试
kill $TEST_PID 2>/dev/null
wait $TEST_PID 2>/dev/null

echo ""
echo "Test 2 completed"

# 测试3: 向后兼容性测试（如果有原始文件）
echo ""
echo "Test 3: Backward compatibility test"
if [ -f "v_10.5.200.218_1723169047302.ts" ] && [ -f "v_10.5.200.218_1723169047302.txt" ]; then
    echo "Found original test files, testing backward compatibility..."
    timeout 5s ./multi_sensor_tool -f test_config.txt -g legacy-test -d &
    TEST_PID=$!
    sleep 3
    kill $TEST_PID 2>/dev/null
    wait $TEST_PID 2>/dev/null
    echo "Backward compatibility test completed"
else
    echo "Original test files not found, skipping backward compatibility test"
    echo "To test backward compatibility, ensure the following files exist:"
    echo "  - v_10.5.200.218_1723169047302.ts"
    echo "  - v_10.5.200.218_1723169047302.txt"
fi

# 测试4: 错误处理测试
echo ""
echo "Test 4: Error handling test"
echo "Testing with invalid configuration..."
./multi_sensor_tool -f nonexistent.txt -g test 2>&1 | head -5
echo ""

echo "Testing with missing group..."
./multi_sensor_tool -f test_config.txt -g nonexistent-group 2>&1 | head -5
echo ""

# 测试5: 命令行参数测试
echo "Test 5: Command line argument test"
echo "Testing missing required parameters..."
./multi_sensor_tool 2>&1 | head -5
echo ""

echo "Testing invalid parameters..."
./multi_sensor_tool -f test_config.txt 2>&1 | head -5
echo ""

echo ""
echo "=== Test Summary ==="
echo "✓ Help message test completed"
echo "✓ JSON sensor configuration test completed"
echo "✓ Error handling test completed"
echo "✓ Command line argument test completed"

if [ -f "v_10.5.200.218_1723169047302.ts" ]; then
    echo "✓ Backward compatibility test completed"
else
    echo "⚠ Backward compatibility test skipped (missing test files)"
fi

echo ""
echo "All tests completed successfully!"
echo ""
echo "To run the tool manually:"
echo "  ./multi_sensor_tool -f test_config.txt -g json-only-test -d"
echo ""
echo "To monitor UDP output:"
echo "  nc -u -l 9001  # For radar data"
echo "  nc -u -l 9002  # For lidar data"
