#include <cstdint>
#include <cstring>
#include <fstream>
#include <iostream>
#include <stdexcept>
#include <string>
#include <vector>

extern "C"
{
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/time.h>
}

// 提取 SEI 时间戳并保存到文件
bool extract_sei_timestamps(const std::string &input_file, const std::string &output_file)
{
    AVFormatContext *format_ctx = nullptr;
    if (avformat_open_input(&format_ctx, input_file.c_str(), nullptr, nullptr) < 0)
    {
        std::cerr << "无法打开输入文件: " << input_file << std::endl;
        return false;
    }

    if (avformat_find_stream_info(format_ctx, nullptr) < 0)
    {
        std::cerr << "无法获取流信息: " << input_file << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }

    int video_stream_index = -1;
    for (unsigned i = 0; i < format_ctx->nb_streams; ++i)
    {
        if (format_ctx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO)
        {
            video_stream_index = i;
            break;
        }
    }

    if (video_stream_index == -1)
    {
        std::cerr << "未找到视频流" << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }

    // 打开输出文件，使用 std::ios::out | std::ios::trunc 模式
    std::ofstream out_file(output_file, std::ios::out | std::ios::trunc);
    if (!out_file.is_open())
    {
        std::cerr << "无法打开输出文件: " << output_file << std::endl;
        avformat_close_input(&format_ctx);
        return false;
    }

    AVPacket pkt;
    while (av_read_frame(format_ctx, &pkt) >= 0)
    {
        if (pkt.stream_index == video_stream_index && pkt.data && pkt.size > 0)
        {
            uint8_t *data = pkt.data;
            size_t size = pkt.size;
            size_t pos = 0;

            while (pos < size)
            {
                if (pos + 3 < size && data[pos] == 0x00 && data[pos + 1] == 0x00 && data[pos + 2] == 0x01)
                {
                    uint8_t nal_type = data[pos + 3] & 0x1F;
                    size_t start_code_len = 3;

                    if (nal_type == 6) // SEI NALU
                    {
                        size_t sei_payload_start = pos + start_code_len;
                        size_t sei_payload_size = 0;

                        // 找到 SEI 数据的结束位置
                        while (sei_payload_start + sei_payload_size < size && data[sei_payload_start + sei_payload_size] != 0x80)
                        {
                            sei_payload_size++;
                        }

                        if (sei_payload_start + sei_payload_size < size)
                        {
                            std::cout << "找到 SEI 数据，大小: " << sei_payload_size << " 字节" << std::endl;

                            // 假设时间戳从 SEI 数据的固定偏移开始
                            const size_t timestamp_offset = 20; // 根据实际 SEI 数据调整
                            const size_t timestamp_size = 14;    // 时间戳长度（字节）

                            if (sei_payload_start + timestamp_offset + timestamp_size <= size)
                            {
                                char timestamp[timestamp_size + 1] = {0};
                                memcpy(timestamp, data + sei_payload_start + timestamp_offset, timestamp_size);
                                timestamp[timestamp_size] = '\0';

                                try
                                {
                                    int64_t timestamp_ms = std::stoll(timestamp);
                                    if (timestamp_ms > 1000000000000)
                                    {
                                        out_file << timestamp_ms << std::endl;
                                        std::cout << "提取时间戳: " << timestamp_ms << std::endl;
                                    }
                                }
                                catch (const std::exception &e)
                                {
                                    std::cerr << "时间戳解析失败: " << e.what() << std::endl;
                                }
                            }
                            else
                            {
                                std::cerr << "时间戳字段超出 SEI 数据范围" << std::endl;
                            }
                        }

                        // 跳过整个 SEI 数据块
                        pos += start_code_len + sei_payload_size + 1;
                        continue;
                    }
                }

                pos++;
            }
        }

        av_packet_unref(&pkt);
    }

    out_file.close();
    avformat_close_input(&format_ctx);
    return true;
}

int main(int argc, char *argv[])
{
    if (argc < 2)
    {
        std::cerr << "用法: " << argv[0]<< " <输入视频文件> [输出时间戳文件]" << std::endl;
        return -1;
    }

    std::string input_file = argv[1];
    std::string output_file;

    // 如果未指定输出文件，则默认与输入文件同名，后缀为 .txt
    if (argc < 3)
    {
        size_t last_dot = input_file.find_last_of('.');
        if (last_dot != std::string::npos)
        {
            output_file = input_file.substr(0, last_dot) + ".txt";
        }
        else
        {
            output_file = input_file + ".txt";
        }
    }
    else
    {
        output_file = argv[2];
    }

    if (extract_sei_timestamps(input_file, output_file))
    {
        std::cout << "SEI 时间戳已成功提取并保存到 " << output_file << std::endl;
    }
    else
    {
        std::cerr << "提取 SEI 时间戳失败" << std::endl;
        return -1;
    }

    return 0;
}
