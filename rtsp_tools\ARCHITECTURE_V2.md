# 多传感器数据同步播放工具 - 架构设计文档 v2.0

## 1. 项目概述

### 1.1 设计目标
基于现有RTSP工具，扩展为支持多传感器数据同步播放的综合工具，包括：
- 普通视频流 (TS格式)
- 鱼眼视频流 (TS格式) 
- Radar数据 (JSON格式)
- Lidar数据 (JSON格式)

### 1.2 核心原则
- **向后兼容**: 保持现有RTSP工具的所有功能
- **模块化设计**: 传感器类型可扩展
- **统一时序**: 所有传感器数据按时间戳统一调度
- **最小改动**: 基于现有代码结构进行扩展

## 2. 整体架构

### 2.1 分层架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   命令行接口     │  │   配置文件解析   │  │   错误处理      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    控制层 (Control Layer)                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  多传感器管理器  │  │   时序调度器     │  │   播放控制器    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data Layer)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   视频处理器     │  │   JSON处理器    │  │   时间戳管理器   │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    输出层 (Output Layer)                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   RTSP推流器     │  │   UDP发送器     │  │   网络管理器    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件关系

```mermaid
graph TB
    A[MultiSensorManager] --> B[TimeScheduler]
    A --> C[VideoSensor]
    A --> D[FisheyeVideoSensor]
    A --> E[RadarSensor]
    A --> F[LidarSensor]
    
    B --> G[TimestampManager]
    
    C --> H[RTSPStreamer]
    D --> H
    E --> I[UDPSender]
    F --> I
    
    J[ConfigParser] --> A
    K[CommandLineInterface] --> A
```

## 3. 核心组件设计

### 3.1 传感器抽象基类

```cpp
class SensorBase {
public:
    enum SensorType { 
        VIDEO,          // 普通视频
        FISHEYE_VIDEO,  // 鱼眼视频
        RADAR,          // Radar数据
        LIDAR           // Lidar数据
    };
    
    enum OutputType {
        RTSP_STREAM,    // RTSP推流
        UDP_PACKET      // UDP数据包
    };
    
    virtual ~SensorBase() = default;
    
    // 核心接口
    virtual bool initialize() = 0;
    virtual bool loadData() = 0;
    virtual bool getNextFrame(int64_t target_timestamp) = 0;
    virtual bool sendFrame() = 0;
    virtual void cleanup() = 0;
    
    // 状态查询
    virtual int64_t getCurrentTimestamp() = 0;
    virtual SensorType getType() = 0;
    virtual OutputType getOutputType() = 0;
    virtual bool isDataAvailable() = 0;
    virtual bool hasMoreData() = 0;
    
    // 配置信息
    virtual std::string getDataFile() = 0;
    virtual std::string getTimestampFile() = 0;
    virtual std::string getOutputAddress() = 0;
    virtual int getOutputPort() = 0;
};
```

### 3.2 具体传感器实现

#### 3.2.1 VideoSensor (视频传感器)
```cpp
class VideoSensor : public SensorBase {
private:
    StreamContext stream_context_;  // 复用现有结构
    // ... 其他成员变量
    
public:
    SensorType getType() override { return VIDEO; }
    OutputType getOutputType() override { return RTSP_STREAM; }
    // 实现基类所有虚函数
};
```

#### 3.2.2 RadarSensor/LidarSensor (JSON数据传感器)
```cpp
class JsonSensorBase : public SensorBase {
protected:
    std::vector<JsonFrame> frames_;
    size_t current_frame_index_;
    std::string udp_address_;
    int udp_port_;
    
    struct JsonFrame {
        int64_t timestamp;
        std::string json_data;
    };
    
public:
    OutputType getOutputType() override { return UDP_PACKET; }
    // JSON解析和UDP发送逻辑
};

class RadarSensor : public JsonSensorBase {
public:
    SensorType getType() override { return RADAR; }
};

class LidarSensor : public JsonSensorBase {
public:
    SensorType getType() override { return LIDAR; }
};
```

### 3.3 时序调度器 (TimeScheduler)

```cpp
class TimeScheduler {
private:
    struct TimeEvent {
        int64_t timestamp;
        SensorBase* sensor;
        size_t sensor_id;
    };
    
    std::priority_queue<TimeEvent> event_queue_;
    std::vector<SensorBase*> sensors_;
    bool is_loop_mode_;
    
public:
    void addSensor(SensorBase* sensor);
    void buildSchedule();
    bool getNextEvent(TimeEvent& event);
    void reset();  // 循环播放重置
};
```

### 3.4 多传感器管理器 (MultiSensorManager)

```cpp
class MultiSensorManager {
private:
    std::vector<std::unique_ptr<SensorBase>> sensors_;
    std::unique_ptr<TimeScheduler> scheduler_;
    bool is_loop_mode_;
    bool is_debug_mode_;
    
public:
    bool loadConfiguration(const std::string& config_file, const std::string& group_name);
    bool initializeAllSensors();
    void startPlayback();
    void stopPlayback();
    
private:
    std::unique_ptr<SensorBase> createSensor(const SensorConfig& config);
};
```

## 4. 配置文件设计

### 4.1 扩展INI格式

```ini
# 多传感器配置文件 - 扩展INI格式
# 支持视频、鱼眼、Radar、Lidar多种传感器

[scene-001]
# 格式: 传感器类型,数据文件,时间戳文件,输出地址,输出端口
video,v_camera1.ts,v_camera1.txt,rtsp://192.168.1.100:8554/stream1,
fisheye,v_fisheye1.ts,v_fisheye1.txt,rtsp://192.168.1.100:8554/stream2,
radar,radar_data.json,,udp://192.168.1.100,9001
lidar,lidar_data.json,,udp://192.168.1.100,9002

[scene-002]
video,v_camera2.ts,v_camera2.txt,rtsp://192.168.1.100:8554/stream3,
radar,radar_data2.json,,udp://192.168.1.100,9003

# 向后兼容 - 原有格式自动识别为video类型
[legacy-group]
v_10.5.200.218_1723169047302.ts,v_10.5.200.218_1723169047302.txt,rtsp://root:root@192.168.50.233:8554/stream1
v_10.5.200.223_1723169047327.ts,v_10.5.200.223_1723169047327.txt,rtsp://root:root@192.168.50.233:8554/stream2
```

### 4.2 配置字段说明

| 字段位置 | 字段名称 | 说明 | 示例 |
|---------|---------|------|------|
| 1 | 传感器类型 | video/fisheye/radar/lidar | video |
| 2 | 数据文件 | 数据文件路径 | camera1.ts |
| 3 | 时间戳文件 | 时间戳文件路径(JSON类型可为空) | camera1.txt |
| 4 | 输出地址 | RTSP地址或UDP地址 | rtsp://192.168.1.100:8554/stream1 |
| 5 | 输出端口 | UDP端口(RTSP可为空) | 9001 |

## 5. 数据格式规范

### 5.1 时间戳文件格式 (视频类型)
```
1723169047372
1723169047412
1723169047452
...
```

### 5.2 JSON数据文件格式 (Radar/Lidar)
```json
[
    {
        "timestamp": 1723169047372,
        "data": {
            "sensor_id": "radar_001",
            "detection_count": 5,
            "detections": [
                {
                    "range": 25.6,
                    "angle": 15.2,
                    "velocity": 12.3
                }
            ]
        }
    },
    {
        "timestamp": 1723169047412,
        "data": {
            "sensor_id": "radar_001",
            "detection_count": 3,
            "detections": [...]
        }
    }
]
```

### 5.3 UDP输出数据格式
```json
{
    "timestamp": 1723169047372,
    "sensor_type": "radar",
    "sensor_id": "radar_001", 
    "data": { /* 原始传感器数据 */ }
}
```

## 6. 命令行接口

### 6.1 扩展命令行参数
```bash
./multi_sensor_tool -f <配置文件> -g <传感器组名称> [-l] [-d] [-t <类型>] [-h]

选项说明：
  -f <配置文件>  必须指定的INI格式配置文件
  -g <组名称>    必须指定要播放的传感器组名称  
  -d            输出调试信息
  -l            循环播放模式
  -h            显示帮助信息
  -t <类型>     设置视频SEI帧类型 (0: 默认, 1: 大华, 2: 海康)
```

### 6.2 使用示例
```bash
# 播放多传感器场景
./multi_sensor_tool -f multi_sensor_config.txt -g scene-001 -l -d

# 向后兼容 - 播放纯视频场景  
./multi_sensor_tool -f rtspconfig.txt -g deqing-001 -l -d
```

## 7. 实现计划

### 7.1 Phase 1: 基础架构 (1-2周)
- [ ] 设计并实现传感器抽象基类
- [ ] 扩展配置文件解析器支持新格式
- [ ] 实现时序调度器核心逻辑
- [ ] 向后兼容性测试

### 7.2 Phase 2: 传感器实现 (2-3周)  
- [ ] 重构现有StreamContext为VideoSensor
- [ ] 实现FisheyeVideoSensor (复用VideoSensor)
- [ ] 实现RadarSensor和LidarSensor
- [ ] 添加UDP输出功能模块

### 7.3 Phase 3: 集成与优化 (1-2周)
- [ ] 多传感器联合播放测试
- [ ] 性能优化和内存管理
- [ ] 错误处理和异常恢复
- [ ] 文档更新和用户手册

## 8. 技术风险与对策

### 8.1 主要风险
| 风险项 | 影响程度 | 对策 |
|-------|---------|------|
| JSON解析性能 | 中 | 使用轻量级JSON库，按需解析 |
| 内存消耗过大 | 高 | 实现LRU缓存，及时释放数据 |
| 时序同步精度 | 中 | 优化调度算法，减少延迟 |
| UDP传输可靠性 | 低 | 添加重传机制(可选) |
| 向后兼容性 | 高 | 充分测试，保持API稳定 |

### 8.2 性能指标
- **内存使用**: 单传感器 < 100MB
- **CPU占用**: 多传感器播放 < 50%
- **时序精度**: ±10ms
- **网络延迟**: UDP < 5ms, RTSP < 50ms

## 9. 测试策略

### 9.1 单元测试
- 各传感器类的独立功能测试
- 配置文件解析器测试
- 时序调度器算法测试

### 9.2 集成测试  
- 多传感器同步播放测试
- 循环播放功能测试
- 网络输出稳定性测试

### 9.3 兼容性测试
- 原有配置文件格式兼容性
- 不同操作系统兼容性
- 不同FFmpeg版本兼容性

---

**文档版本**: v2.0  
**创建日期**: 2025-01-XX  
**最后更新**: 2025-01-XX  
**维护者**: 架构团队
