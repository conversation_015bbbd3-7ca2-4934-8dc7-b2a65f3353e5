#ifndef RTSP_SERVER_H
#define RTSP_SERVER_H

#include <chrono>
#include <iostream>
#include <thread>
#include <vector>
#include <fstream>
#include <string>
#include <cstdint>
#include <stdexcept>
#include <sstream>
#include <algorithm>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/time.h>
}

#define MAX_RETRIES 3

struct StreamContext {
  AVFormatContext *input_ctx = nullptr;
  AVFormatContext *output_ctx = nullptr;
  AVPacket pkt;
  int64_t last_dts[AV_NUM_DATA_POINTERS] = {0};
  int retry_count = 0;
  std::string input_file_ts;
  std::string input_file_txt;
  std::vector<int64_t> time_base;
  int64_t frame_seq = 0;
  int64_t correction_frame_seq = 0;
  std::string output_url;
  int64_t average_interval = 3600;
  int64_t average_interval_ms = 40;
  int64_t time_count_ms = 0;
  bool is_playing = false;

  // 构造函数
  StreamContext(const std::string &input_ts, const std::string &input_txt, const std::string &output)
      : input_file_ts(input_ts), input_file_txt(input_txt), output_url(output) {}
};

class RtspServer {
public:
  RtspServer(std::vector<StreamContext> &streams, bool is_loop = false, bool is_debug = false, uint16_t camera_type = 0);
  ~RtspServer();

  void push_stream_loop();
  void reload_stream(StreamContext &stream);
  void reset_all_stream();
  void handle_timestamps(StreamContext &stream);
  void process_packet(AVPacket *pkt, StreamContext &stream);
  uint8_t* create_sei_frame(size_t *out_size, const StreamContext &stream);
  
private:
  int init_stream(StreamContext &stream);
  std::vector<int64_t> readFileToInt64(const std::string& filePath);
  int64_t get_timestamp_ms();
  void streams_alignment();   // 时间对齐
  
  std::vector<StreamContext> streams_;
  int64_t earliest_timestamp_ = INT64_MAX;
  bool is_loop_ = false;
  bool is_debug_ = false;
  bool is_playing_ = false;
  uint32_t camera_type_ = 0;  // 摄像头类型, 不添加sei帧
  int64_t alignment_period_ = 1000;     // 误差修正周期
  int64_t correction_deviation_ = 100;  // ms 误差修正
};

#endif // RTSP_SERVER_H
