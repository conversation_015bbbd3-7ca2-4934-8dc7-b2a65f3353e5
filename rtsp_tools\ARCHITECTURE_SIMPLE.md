# 多传感器数据同步播放工具 - 简化架构设计文档

## 1. 项目概述

### 1.1 设计目标
基于现有RTSP工具，采用**最小化扩展**策略，快速实现多传感器数据同步播放功能：
- 普通视频流 (TS格式)
- 鱼眼视频流 (TS格式) 
- Radar数据 (JSON格式)
- Lidar数据 (JSON格式)

### 1.2 核心原则
- **最小改动**: 保持原有代码结构和逻辑不变
- **简单直接**: 避免过度设计，优先稳定性
- **快速实现**: 3周内完成开发和测试
- **向后兼容**: 完全兼容现有功能和配置

### 1.3 技术策略
- **复用原有时序逻辑**: 保持2ms轮询机制
- **简化内存管理**: JSON数据全量加载，视频复用FFmpeg管理
- **最小抽象层**: 只添加必要的抽象接口
- **渐进式实现**: 分阶段交付，降低风险

## 2. 整体架构

### 2.1 架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    应用层 (Application Layer)                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   命令行接口     │  │   配置文件解析   │  │   错误处理      │ │
│  │   (保持不变)     │  │   (扩展格式)     │  │   (简化版)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    控制层 (Control Layer)                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  多传感器管理器  │  │   时序控制器     │  │   播放控制器    │ │
│  │  (新增)         │  │   (复用原逻辑)   │  │   (复用原逻辑)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data Layer)                      │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   视频传感器     │  │   JSON传感器    │  │   时间戳管理器   │ │
│  │   (包装原逻辑)   │  │   (新增)        │  │   (复用原逻辑)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    输出层 (Output Layer)                    │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   RTSP推流器     │  │   UDP发送器     │  │   网络管理器    │ │
│  │   (复用原逻辑)   │  │   (新增)        │  │   (简化版)      │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 组件关系

```mermaid
graph TB
    A[SimpleMultiSensorManager] --> B[VideoSensor]
    A --> C[JsonSensor]
    
    B --> D[StreamContext<br/>复用原有结构]
    B --> E[RTSPStreamer<br/>复用原有逻辑]
    
    C --> F[JsonFrameLoader]
    C --> G[UDPSender]
    
    H[ConfigParser<br/>扩展解析] --> A
    I[CommandLineInterface<br/>保持不变] --> A
```

## 3. 核心组件设计

### 3.1 传感器抽象接口（最小化）

```cpp
class SensorBase {
public:
    enum SensorType { VIDEO, FISHEYE_VIDEO, RADAR, LIDAR };
    
    virtual ~SensorBase() = default;
    
    // 核心接口（最小化）
    virtual bool initialize() = 0;
    virtual bool processFrame(int64_t current_time) = 0;
    virtual int64_t getCurrentTimestamp() = 0;
    virtual bool hasMoreData() = 0;
    virtual SensorType getType() = 0;
    
    // 状态查询
    virtual bool isInitialized() const = 0;
    virtual std::string getDataFile() const = 0;
    virtual std::string getOutputAddress() const = 0;
};
```

### 3.2 视频传感器（包装原有逻辑）

```cpp
class VideoSensor : public SensorBase {
private:
    StreamContext stream_context_;  // 直接复用原有结构
    SensorType sensor_type_;        // VIDEO 或 FISHEYE_VIDEO
    
public:
    VideoSensor(const std::string& ts_file, const std::string& txt_file, 
                const std::string& rtsp_url, SensorType type = VIDEO);
    
    // 实现接口，内部调用原有逻辑
    bool initialize() override;
    bool processFrame(int64_t current_time) override;
    int64_t getCurrentTimestamp() override;
    bool hasMoreData() override;
    SensorType getType() override { return sensor_type_; }
    
private:
    // 包装原有的处理逻辑
    bool shouldProcessFrame(int64_t current_time);
    void handleVideoFrame();
};
```

### 3.3 JSON传感器（新增，简化实现）

```cpp
class JsonSensor : public SensorBase {
public:
    struct JsonFrame {
        int64_t timestamp;
        std::string data;
    };
    
private:
    std::vector<JsonFrame> frames_;     // 全量加载，简单直接
    size_t current_index_ = 0;
    SensorType sensor_type_;            // RADAR 或 LIDAR
    std::string udp_address_;
    int udp_port_;
    int udp_socket_ = -1;
    
public:
    JsonSensor(const std::string& json_file, const std::string& udp_address, 
               int udp_port, SensorType type);
    
    bool initialize() override;
    bool processFrame(int64_t current_time) override;
    int64_t getCurrentTimestamp() override;
    bool hasMoreData() override;
    SensorType getType() override { return sensor_type_; }
    
private:
    bool loadJsonData();
    bool sendUdpData(const std::string& data);
    void parseJsonFile(const std::string& file_path);
};
```

### 3.4 多传感器管理器（新增，复用原有时序逻辑）

```cpp
class SimpleMultiSensorManager {
private:
    std::vector<std::unique_ptr<SensorBase>> sensors_;
    int64_t earliest_timestamp_ = INT64_MAX;    // 复用原有对齐逻辑
    bool is_loop_ = false;
    bool is_debug_ = false;
    uint16_t camera_type_ = 0;
    
public:
    SimpleMultiSensorManager(bool is_loop = false, bool is_debug = false, 
                           uint16_t camera_type = 0);
    
    bool loadConfiguration(const std::string& config_file, const std::string& group_name);
    bool initializeAllSensors();
    void startPlayback();  // 复用原有的播放循环逻辑
    void stopPlayback();
    
private:
    std::unique_ptr<SensorBase> createSensor(const SensorConfig& config);
    void findEarliestTimestamp();  // 复用原有对齐逻辑
    int64_t get_timestamp_ms();    // 复用原有时间函数
    
    // 复用原有的播放循环逻辑
    void playbackLoop();
    bool shouldProcessSensor(SensorBase* sensor, int64_t current_time, int64_t time_interval);
};
```

## 4. 配置文件设计（简化扩展）

### 4.1 扩展INI格式（向后兼容）

```ini
# 多传感器配置文件 - 简化扩展格式
# 完全向后兼容原有格式

[scene-001]
# 新格式：类型前缀 + 原有字段格式
video:v_camera1.ts,v_camera1.txt,rtsp://192.168.1.100:8554/stream1
fisheye:v_fisheye1.ts,v_fisheye1.txt,rtsp://192.168.1.100:8554/stream2
radar:radar_data.json,udp://192.168.1.100:9001
lidar:lidar_data.json,udp://192.168.1.100:9002

[scene-002]
video:v_camera2.ts,v_camera2.txt,rtsp://192.168.1.100:8554/stream3
radar:radar_data2.json,udp://192.168.1.100:9003

# 向后兼容 - 原有格式自动识别为video类型
[legacy-group]
v_10.5.200.218_1723169047302.ts,v_10.5.200.218_1723169047302.txt,rtsp://root:root@**************:8554/stream1
v_10.5.200.223_1723169047327.ts,v_10.5.200.223_1723169047327.txt,rtsp://root:root@**************:8554/stream2
```

### 4.2 配置解析逻辑（扩展原有解析器）

```cpp
class SimpleConfigParser {
public:
    struct SensorConfig {
        std::string type;           // "video", "fisheye", "radar", "lidar"
        std::string data_file;      // 数据文件路径
        std::string timestamp_file; // 时间戳文件路径（JSON类型为空）
        std::string output_address; // 输出地址
        int output_port = 0;        // UDP端口
    };
    
    std::vector<SensorConfig> parseGroup(const std::string& file_path, 
                                       const std::string& group_name);
    
private:
    bool isLegacyFormat(const std::string& line);
    SensorConfig parseLegacyLine(const std::string& line);
    SensorConfig parseNewFormatLine(const std::string& line);
};
```

## 5. 数据格式规范

### 5.1 时间戳文件格式（保持不变）
```
1723169047372
1723169047412
1723169047452
...
```

### 5.2 JSON数据文件格式（简化）
```json
[
    {
        "timestamp": 1723169047372,
        "data": {
            "sensor_id": "radar_001",
            "detections": [
                {"range": 25.6, "angle": 15.2, "velocity": 12.3}
            ]
        }
    },
    {
        "timestamp": 1723169047412,
        "data": {
            "sensor_id": "radar_001", 
            "detections": [...]
        }
    }
]
```

### 5.3 UDP输出格式（简化）
```json
{
    "timestamp": 1723169047372,
    "sensor_type": "radar",
    "data": { /* 原始JSON数据 */ }
}
```

## 6. 时序控制（完全复用原有逻辑）

### 6.1 时序同步机制（保持不变）

```cpp
void SimpleMultiSensorManager::playbackLoop() {
    // 完全复用原有的时序控制逻辑
    auto time_now = get_timestamp_ms();
    uint64_t time_interval = time_now - earliest_timestamp_;
    
    while (true) {
        time_now = get_timestamp_ms();
        
        // 遍历所有传感器（扩展原有的流遍历逻辑）
        for (auto& sensor : sensors_) {
            // 复用原有的时间判断逻辑
            if (shouldProcessSensor(sensor.get(), time_now, time_interval)) {
                sensor->processFrame(time_now);
            }
        }
        
        // 保持原有的休眠间隔
        std::this_thread::sleep_for(std::chrono::milliseconds(2));
    }
}
```

### 6.2 时序精确性保证

- **轮询精度**: 保持2ms轮询间隔
- **时间基准**: 复用原有的系统时间基准
- **对齐机制**: 复用原有的earliest_timestamp_对齐
- **最大延迟**: 保持2ms最大延迟

## 7. 内存管理（简化策略）

### 7.1 简化内存管理原则

```cpp
class SimpleMemoryManager {
private:
    static constexpr size_t MAX_JSON_FILE_SIZE = 100 * 1024 * 1024; // 100MB限制
    static constexpr size_t MAX_TOTAL_JSON_MEMORY = 500 * 1024 * 1024; // 总计500MB
    
public:
    // 简单的内存检查
    bool checkMemoryLimit(size_t required_size) {
        return required_size <= MAX_JSON_FILE_SIZE;
    }
    
    // JSON数据全量加载（简单直接）
    bool loadJsonFile(const std::string& file_path, std::vector<JsonFrame>& frames) {
        std::ifstream file(file_path);
        if (!file.is_open()) return false;
        
        // 检查文件大小
        file.seekg(0, std::ios::end);
        size_t file_size = file.tellg();
        if (!checkMemoryLimit(file_size)) {
            return false;
        }
        
        // 全量加载，不做复杂缓存
        file.seekg(0, std::ios::beg);
        // ... 解析逻辑
        return true;
    }
};
```

### 7.2 内存使用策略

| 数据类型 | 管理策略 | 内存限制 |
|---------|---------|---------|
| 视频数据 | 复用FFmpeg内存管理 | 无额外限制 |
| JSON数据 | 全量加载到内存 | 单文件100MB，总计500MB |
| 时间戳数据 | 全量加载（复用原逻辑） | 无限制（通常很小） |

## 8. 错误处理（简化版）

### 8.1 分级错误处理

```cpp
class SimpleErrorHandler {
public:
    enum ErrorLevel { INFO, WARNING, ERROR, FATAL };
    
    static void logError(ErrorLevel level, const std::string& message) {
        std::string prefix = getErrorPrefix(level);
        std::cerr << prefix << message << std::endl;
        
        if (level == FATAL) {
            exit(1);  // 简单直接的错误处理
        }
    }
    
    static bool retryOperation(std::function<bool()> operation, int max_retries = 3) {
        for (int i = 0; i < max_retries; ++i) {
            if (operation()) {
                return true;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        return false;
    }
    
private:
    static std::string getErrorPrefix(ErrorLevel level);
};

### 8.2 错误恢复策略

| 错误类型 | 处理策略 | 恢复方式 |
|---------|---------|---------|
| 配置文件错误 | 立即退出 | 用户修正配置 |
| JSON文件解析错误 | 跳过该传感器 | 记录错误日志 |
| UDP发送失败 | 重试3次 | 失败后跳过该帧 |
| 视频文件错误 | 复用原有处理 | 原有重试机制 |

## 9. 网络输出（简化实现）

### 9.1 UDP发送器（新增）

```cpp
class SimpleUDPSender {
private:
    int socket_fd_ = -1;
    struct sockaddr_in server_addr_;
    std::string target_address_;
    int target_port_;

public:
    SimpleUDPSender(const std::string& address, int port);
    ~SimpleUDPSender();

    bool initialize();
    bool sendData(const std::string& data);
    void cleanup();

private:
    bool createSocket();
    bool setupAddress();
};
```

### 9.2 网络输出策略

- **RTSP输出**: 完全复用原有FFmpeg推流逻辑
- **UDP输出**: 简单的UDP socket发送，无复杂重传机制
- **错误处理**: 发送失败时记录日志，跳过该帧继续播放

## 10. 实现计划

### 10.1 开发阶段

#### Phase 1: 基础框架 (第1周)
- [ ] 扩展配置文件解析器
- [ ] 实现传感器抽象接口
- [ ] 实现VideoSensor（包装原有逻辑）
- [ ] 基础的SimpleMultiSensorManager

#### Phase 2: JSON传感器和UDP输出 (第2周)
- [ ] 实现JsonSensor类
- [ ] 实现SimpleUDPSender
- [ ] JSON文件解析和验证
- [ ] 集成测试（视频+JSON混合播放）

#### Phase 3: 完善和测试 (第3周)
- [ ] 错误处理和日志系统
- [ ] 循环播放功能测试
- [ ] 向后兼容性测试
- [ ] 性能测试和优化
- [ ] 文档更新

### 10.2 测试策略

#### 10.2.1 单元测试
```cpp
class SimpleTestSuite {
public:
    void testConfigParsing();           // 配置文件解析测试
    void testVideoSensorWrapper();     // 视频传感器包装测试
    void testJsonSensorBasic();        // JSON传感器基础测试
    void testUDPSending();             // UDP发送测试
    void testBackwardCompatibility();  // 向后兼容性测试
};
```

#### 10.2.2 集成测试
- **混合播放测试**: 视频+JSON传感器同时播放
- **时序精确性测试**: 验证时间戳同步精度
- **循环播放测试**: 验证循环播放功能
- **错误恢复测试**: 验证各种错误场景的处理

#### 10.2.3 性能测试
- **内存使用测试**: 监控内存使用情况
- **CPU占用测试**: 确保CPU占用在合理范围
- **网络延迟测试**: 测试UDP发送延迟

## 11. 命令行接口（保持不变）

### 11.1 命令行参数（完全兼容）

```bash
./multi_sensor_tool -f <配置文件> -g <传感器组名称> [-l] [-d] [-t <类型>] [-h]

选项说明：
  -f <配置文件>  必须指定的INI格式配置文件
  -g <组名称>    必须指定要播放的传感器组名称
  -d            输出调试信息
  -l            循环播放模式
  -h            显示帮助信息
  -t <类型>     设置视频SEI帧类型 (0: 默认, 1: 大华, 2: 海康)
```

### 11.2 使用示例

```bash
# 播放多传感器场景
./multi_sensor_tool -f multi_sensor_config.txt -g scene-001 -l -d

# 向后兼容 - 播放纯视频场景
./multi_sensor_tool -f rtspconfig.txt -g deqing-001 -l -d

# 播放包含JSON传感器的场景
./multi_sensor_tool -f config.txt -g radar-test -d
```

## 12. 技术风险与对策

### 12.1 主要风险评估

| 风险项 | 影响程度 | 概率 | 对策 |
|-------|---------|------|------|
| JSON解析性能 | 低 | 低 | 全量加载，避免实时解析 |
| UDP发送稳定性 | 中 | 中 | 简单重试机制 |
| 向后兼容性 | 高 | 低 | 充分测试原有功能 |
| 内存使用过大 | 中 | 低 | 文件大小限制 |
| 时序精确性 | 低 | 低 | 完全复用原有逻辑 |

### 12.2 风险缓解措施

1. **充分测试**: 每个阶段都进行充分的单元测试和集成测试
2. **渐进实现**: 分阶段交付，及时发现和解决问题
3. **简单设计**: 避免复杂逻辑，降低出错概率
4. **复用成熟代码**: 最大化复用原有稳定逻辑

## 13. 性能指标

### 13.1 目标指标

- **开发时间**: 3周内完成
- **代码增量**: < 1500行新增代码
- **内存使用**: JSON传感器 < 100MB，总体增量 < 500MB
- **CPU占用**: 增量 < 10%
- **时序精度**: 保持原有2ms精度
- **网络延迟**: UDP < 10ms

### 13.2 质量指标

- **代码覆盖率**: > 80%
- **向后兼容性**: 100%兼容原有功能
- **稳定性**: 连续运行24小时无崩溃
- **错误恢复**: 单个传感器错误不影响其他传感器

## 14. 部署和维护

### 14.1 编译要求（保持不变）

- 支持C++11的编译器（GCC 4.8+）
- 已安装FFmpeg开发库
- 系统支持标准socket库

### 14.2 编译方式（扩展原有脚本）

```bash
# 使用扩展的编译脚本
chmod +x build_multi_sensor.sh
./build_multi_sensor.sh
```

### 14.3 运行环境要求

- **操作系统**: Linux/Windows（与原工具相同）
- **内存**: 建议2GB以上可用内存
- **网络**: 支持UDP和RTSP协议
- **磁盘**: 足够存储视频和JSON数据文件

## 15. 总结

### 15.1 方案优势

1. **快速实现**: 3周开发周期，风险可控
2. **稳定可靠**: 最大化复用原有稳定逻辑
3. **向后兼容**: 100%兼容现有功能
4. **易于维护**: 代码简单清晰，易于理解和调试
5. **渐进扩展**: 为未来功能扩展预留空间

### 15.2 适用场景

- **快速原型验证**: 需要快速验证多传感器播放概念
- **稳定性优先**: 对系统稳定性要求高于性能优化
- **资源有限**: 开发时间和人力资源有限
- **兼容性要求**: 需要保持与现有系统的完全兼容

### 15.3 未来扩展方向

在基础功能稳定后，可以考虑以下扩展：
- 性能优化（如需要）
- 更复杂的内存管理（如需要）
- 图形化监控界面（如需要）
- 更多传感器类型支持（如需要）

---

**文档版本**: v1.0 (简化版)
**创建日期**: 2025-01-XX
**预计完成**: 2025-01-XX + 3周
**维护者**: 开发团队
```
