# Windows环境编译指南

## 环境准备

### 方法1: 使用MSYS2（推荐）

1. **安装MSYS2**
   - 下载: https://www.msys2.org/
   - 安装后打开MSYS2终端

2. **安装编译工具和依赖**
   ```bash
   # 更新包管理器
   pacman -Syu
   
   # 安装编译工具
   pacman -S mingw-w64-x86_64-gcc
   pacman -S mingw-w64-x86_64-make
   pacman -S mingw-w64-x86_64-pkg-config
   
   # 安装FFmpeg开发库
   pacman -S mingw-w64-x86_64-ffmpeg
   
   # 安装JsonCpp库
   pacman -S mingw-w64-x86_64-jsoncpp
   ```

3. **设置环境变量**
   - 将 `C:\msys64\mingw64\bin` 添加到系统PATH

4. **编译**
   ```bash
   # 在MSYS2终端中
   cd /path/to/rtsp_tools
   make
   ```

### 方法2: 使用Visual Studio

1. **安装Visual Studio 2019/2022**
   - 包含C++开发工具

2. **安装vcpkg包管理器**
   ```cmd
   git clone https://github.com/Microsoft/vcpkg.git
   cd vcpkg
   .\bootstrap-vcpkg.bat
   .\vcpkg integrate install
   ```

3. **安装依赖库**
   ```cmd
   .\vcpkg install ffmpeg:x64-windows
   .\vcpkg install jsoncpp:x64-windows
   ```

4. **使用Visual Studio编译**
   - 创建新的C++项目
   - 添加源文件
   - 配置包含目录和库目录

### 方法3: 使用预编译库

如果编译困难，可以考虑：
1. 使用Docker容器编译
2. 在Linux虚拟机中编译
3. 使用GitHub Actions自动编译

## Windows特定修改

### 1. 网络库兼容性

代码已经包含Windows网络库支持：
```cpp
#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
#endif
```

### 2. 文件路径处理

Windows使用反斜杠路径分隔符，代码已兼容。

### 3. 信号处理

Windows不支持某些UNIX信号，代码已适配：
```cpp
#ifndef _WIN32
    signal(SIGQUIT, signalHandler);
#endif
```

## 编译命令

### MSYS2环境
```bash
g++ -std=c++11 -O2 -g -Wall -Wextra \
    -o multi_sensor_tool.exe \
    multi_sensor_main.cpp multi_sensor_server.cpp \
    $(pkg-config --cflags --libs libavformat libavcodec libavutil jsoncpp) \
    -lws2_32 -lpthread
```

### Visual Studio命令行
```cmd
cl /EHsc /std:c++11 /I"path\to\ffmpeg\include" /I"path\to\jsoncpp\include" ^
   multi_sensor_main.cpp multi_sensor_server.cpp ^
   /link "path\to\ffmpeg\lib\avformat.lib" "path\to\ffmpeg\lib\avcodec.lib" ^
   "path\to\ffmpeg\lib\avutil.lib" "path\to\jsoncpp\lib\jsoncpp.lib" ^
   ws2_32.lib
```

## 测试

### 1. 生成测试数据

创建 `generate_test_data.bat`:
```batch
@echo off
echo Generating test data...

echo [{"timestamp": 1723169047000, "data": {"sensor_id": "radar_001", "test": true}}] > test_radar.json
echo [{"timestamp": 1723169047000, "data": {"sensor_id": "lidar_001", "test": true}}] > test_lidar.json

echo Test data generated.
```

### 2. 运行测试

```cmd
# 生成测试数据
generate_test_data.bat

# 运行工具
multi_sensor_tool.exe -f test_config.txt -g json-only-test -d
```

### 3. UDP监听测试

Windows下可以使用PowerShell或第三方工具：

**PowerShell UDP监听器**:
```powershell
# 创建UDP监听器脚本 udp_listener.ps1
$endpoint = New-Object System.Net.IPEndPoint([System.Net.IPAddress]::Any, 9001)
$udpClient = New-Object System.Net.Sockets.UdpClient(9001)

Write-Host "Listening on UDP port 9001..."
while ($true) {
    $receivedBytes = $udpClient.Receive([ref]$endpoint)
    $receivedData = [System.Text.Encoding]::UTF8.GetString($receivedBytes)
    Write-Host "$(Get-Date -Format 'HH:mm:ss.fff'): $receivedData"
}
```

运行监听器：
```cmd
powershell -ExecutionPolicy Bypass -File udp_listener.ps1
```

## 故障排除

### 1. 编译错误

**错误**: `fatal error: json/json.h: No such file or directory`
**解决**: 
- MSYS2: `pacman -S mingw-w64-x86_64-jsoncpp`
- vcpkg: `vcpkg install jsoncpp:x64-windows`

**错误**: `fatal error: libavformat/avformat.h: No such file or directory`
**解决**:
- MSYS2: `pacman -S mingw-w64-x86_64-ffmpeg`
- vcpkg: `vcpkg install ffmpeg:x64-windows`

### 2. 运行时错误

**错误**: `WSAStartup failed`
**解决**: 确保链接了ws2_32.lib库

**错误**: `Cannot bind to UDP port`
**解决**: 
- 检查防火墙设置
- 确认端口未被占用
- 以管理员权限运行

### 3. 路径问题

Windows路径使用反斜杠，配置文件中的路径需要注意：
```ini
[test-scene]
radar:C:\path\to\test_radar.json,udp://127.0.0.1:9001
```

或使用正斜杠（通常也能工作）：
```ini
[test-scene]
radar:C:/path/to/test_radar.json,udp://127.0.0.1:9001
```

## 性能优化

### 1. 编译优化
```bash
# 添加优化标志
g++ -std=c++11 -O3 -DNDEBUG -march=native ...
```

### 2. 运行时优化
- 使用SSD存储数据文件
- 确保足够的内存
- 关闭不必要的后台程序

## 部署

### 1. 依赖库打包
编译后需要确保目标机器有必要的DLL文件：
- FFmpeg DLL文件
- JsonCpp DLL文件
- MSVC运行时库

### 2. 创建安装包
可以使用NSIS或Inno Setup创建Windows安装包。

## 替代方案

如果Windows编译困难，可以考虑：

### 1. Docker方案
```dockerfile
FROM ubuntu:20.04
RUN apt-get update && apt-get install -y \
    libavformat-dev libavcodec-dev libavutil-dev \
    libjsoncpp-dev pkg-config build-essential
COPY . /app
WORKDIR /app
RUN make
```

### 2. WSL方案
在Windows Subsystem for Linux中编译：
```bash
# 安装WSL2
# 在WSL中安装Ubuntu
# 按照Linux方式编译
```

### 3. 虚拟机方案
在VirtualBox或VMware中运行Linux进行编译。
